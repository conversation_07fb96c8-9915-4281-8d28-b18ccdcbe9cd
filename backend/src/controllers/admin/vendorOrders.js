'use strict';

const config = require('../../services/config');
const mongoose = require('mongoose');
const ObjectId = mongoose.Types.ObjectId;
const Bluebird = require('bluebird');
const constants = require('../../models/constants');
const {HttpError} = require('../../services/error');
const regexEscape = require('escape-string-regexp');
const mw = require('../../services/middleware');
const Router = require('koa-router');

module.exports = Router()
    .prefix('/vendor-orders')
    .param('vendorOrderKey', paramVendorOrder)
    .get('/', getVendorOrderList)
    .get('/:vendorOrderKey', getVendorOrder)
    .patch('/:vendorOrderKey', patchVendorOrder);


async function paramVendorOrder(vendorOrderKey, ctx, next) {
    const {VendorOrder} = mongoose.models;
    const vendorOrder = await VendorOrder.findOne({key: vendorOrderKey}).populate('_vendor _retailer _retailerOrder');

    if (!vendorOrder) {
        throw new HttpError(404, 'Vendor order not found');
    }
    ctx.state.vendorOrder = vendorOrder;

    await next();
}

async function getVendorOrderList(ctx) {
    const {VendorOrder, RetailerOrder} = mongoose.models;
    const payload = ctx.request.query;
    const query = {};
    const sort = payload.sort || '-createdAt';

    if (payload.status) {
        query.status = payload.status;
    }
    if (payload['q-vendor'] || payload['q-retailer'] || payload['q-vendor-order'] || payload['q-retailer-order']) {
        query.$and = [];
    }
    if (payload['q-vendor-order']) {
        const regex = new RegExp(regexEscape(payload['q-vendor-order']), 'i');
        query.$and.push(
            {key: {$regex: regex}}
        );
    }
    if (payload['q-retailer-order']) {
        const regex = new RegExp(regexEscape(payload['q-retailer-order']), 'i');
        query.$and.push(
            {'retailerOrder.key': {$regex: regex}}
        );
    }
    if (payload['q-retailer']) {
        const regex = new RegExp(regexEscape(payload['q-retailer']), 'i');
        query.$and.push(
            {$or: [
                {'retailer.key': {$regex: regex}},
                {'retailer.businessName': {$regex: regex}},
                {'retailerUser.email': {$regex: regex}}
            ]}
        );
    }
    if (payload['q-vendor']) {
        const regex = new RegExp(regexEscape(payload['q-vendor']), 'i');
        query.$and.push(
            {$or: [
                {'vendor.key': {$regex: regex}},
                {'vendor.storeName': {$regex: regex}},
                {'vendor.slug': {$regex: regex}},
                {'vendorUser.email': {$regex: regex}}
            ]}
        );
    }


    if (ctx.state.currentState) {
        query._state = ctx.state.currentState._id;
    }

    if (payload.retailerOrder) {
        const retailerOrder = await RetailerOrder.findOne({key: payload.retailerOrder});
        if (retailerOrder) {
            query._retailerOrder = retailerOrder._id;
        }
    }
    const {list, count, total} = await Bluebird.props({
        list: adminVendorOrderList(query, sort)
            .skip((payload.page -1) * payload.perPage)
            .limit(payload.perPage)
            .exec(),

        count: adminVendorOrderList(query, sort).count('count'),
        total: VendorOrder.count({})
    });
    ctx.body = {
        data: list,
        pagination: {
            count: count[0] && count[0].count || 0,
            total,
            page: payload.page,
            perPage: payload.perPage
        },
        filters: {...payload}
    };
}
async function getVendorOrder(ctx) {
    const {vendorOrder} = ctx.state;

    // await vendorOrder.populate('_productOrders').execPopulate();

    ctx.body = {
        data: vendorOrder.formatAdmin()
    };
}
async function patchVendorOrder(ctx) {
    const {SystemLog} = mongoose.models;
    const {status} = ctx.request.body;
    const {vendorOrder} = ctx.state;

    const oldStatus = vendorOrder.status;
    if (status && status !== vendorOrder.status) {
        vendorOrder.set({status, processedAt: new Date()});
    }
    await vendorOrder.save();
    SystemLog.adminVendorOrderStatus(ctx, vendorOrder, oldStatus, vendorOrder.status);
    ctx.body = {
        data: vendorOrder.formatAdmin()
    };
}

function adminVendorOrderList(query, sort) {
    const {VendorOrder} = mongoose.models;
    sort = {
        [sort.split('-').reverse()[0]]: sort.startsWith('-') ? -1 : 1
    };

    return VendorOrder.aggregate([
        {
            $lookup:
                {
                    from: 'vendors',
                    localField: '_vendor',
                    foreignField: '_id',
                    as: 'vendor'
                }
        },
        {
            $lookup:
                {
                    from: 'retailers',
                    localField: '_retailer',
                    foreignField: '_id',
                    as: 'retailer'
                }
        },
        {
            $lookup:
                {
                    from: 'retailerOrders',
                    localField: '_retailerOrder',
                    foreignField: '_id',
                    as: 'retailerOrder'
                }
        },
        {
            $addFields: {
                vendor: {$arrayElemAt: ['$vendor', 0]},
                retailer: {$arrayElemAt: ['$retailer', 0]},
                retailerOrder: {$arrayElemAt: ['$retailerOrder', 0]}
            }
        },
        {
            $match: query
        },
        {
            $sort: sort
        },
        {
            $project: {
                _id: 0,
                key: '$key',
                retailerOrder: {
                    key: 1
                },
                vendor: {
                    key: '$vendor.key',
                    storeName: '$vendor.storeName',
                    slug: '$vendor.slug'
                },
                retailer: {
                    key: '$retailer.key',
                    businessName: '$retailer.businessName'
                },
                itemsPrice: 1,
                deliveryPrice: 1,
                totalPrice: 1,
                shippingFee: 1,

                status: 1,
                state: '$_state',
                createdAt: 1,
                updatedAt: 1
            }
        }
    ]);
}
