'use strict';

const config = require('../../services/config');
const mongoose = require('mongoose');
const ObjectId = mongoose.Types.ObjectId;
const constants = require('../../models/constants');
const Bluebird = require('bluebird');
const { HttpError } = require('../../services/error');
const escapeRegex = require('escape-string-regexp');
const mw = require('../../services/middleware');
const pick = require('lodash/pick');
const campaignTriggers = require('../../services/campaign-triggers');
const Router = require('koa-router');


module.exports = Router()
    .prefix('/products')
    .param('productKey', paramProduct)
    .get('/', getProducts)
    .get('/:productKey', getProduct)
    .patch('/:productKey', patchProduct)
    .delete('/:productKey', deleteProduct)
    .post('/', postProduct);

async function paramProduct(productKey, ctx, next) {
    const { Product } = mongoose.models;
    if (ctx.request.headers.accept.includes('text/html')) {
        return await next();
    }
    ctx.state.product = await Product.findOne({
        key: productKey,
        _vendor: ctx.state.vendor,
        deletedAt: { $exists: false }
    }).populate('_vendor');

    if (!ctx.state.product) {
        throw new HttpError(404, 'Product not found');
    }

    await next();
}

// Controller: Get all products in the payload based on the vendor.
async function getProducts(ctx) {
    const { Product } = mongoose.models;
    const payload = ctx.request.query;
    const query = {
        _vendor: ctx.state.vendor,
        deletedAt: { $exists: false }
    };

    // Used for sorting products
    const sort = payload.sort || '-createdAt';

    if (payload.q) {
        query.name = { $regex: new RegExp(escapeRegex(payload.q), 'i') };
    }

    if (payload.status) {
        query.status = payload.status;
    }
    
    if (payload.type) {
        query.type = payload.type;
    }

    // TODO: Find out why this is commented out?    
    // if (payload.effect && !Array.isArray(payload.effect)) {
    //     payload.effect = [payload.effect];
    // }
    // if (payload.usage && !Array.isArray(payload.usage)) {
    //     payload.usage = [payload.usage];
    // }
    // if (payload.flower && !Array.isArray(payload.flower)) {
    //     payload.flower = [payload.flower];
    // }
    // if (payload.environment && !Array.isArray(payload.environment)) {
    //     payload.environment = [payload.environment];
    // }

    const response = await Bluebird.props({
        list: Product
            .find(query)
            .sort(sort)
            .paginate(payload.page, payload.perPage)
            .exec(),

        count: Product.count(query),
        total: Product.count({ _vendor: ctx.state.vendor })
    });

    ctx.body = {
        data: response.list.map(product => product.formatPublic()),
        pagination: {
            count: response.count,
            total: response.total,
            page: payload.page,
            perPage: payload.perPage
        },
        filters: { ...payload }
    };
}

// Controller: Get a specific product based on id
async function getProduct(ctx) {
    const { Review } = mongoose.models;
    const { product } = ctx.state;

    if (!product) {
        throw new HttpError(404, 'Product not found');
    }

    const bestReviewList = await Review.find({
        _product: product._id,
        deletedAt: { $exists: false },
        productComment: { $exists: true },
        productRating: { $in: [4, 5] }
    })
        .limit(3)
        .populate('_retailer');

    const reviewList = await Review.find({
        _product: product._id,
        deletedAt: { $exists: false },
        productComment: { $exists: true }
    })
        .limit(6)
        .populate('_retailer');

    const vendorReviewList = await Review.find({
        _vendor: product._vendor._id,
        deletedAt: { $exists: false },
        vendorComment: { $exists: true }
    })
        .limit(6)
        .populate('_retailer');

    const formattedProduct = await product.formatSource();

    formattedProduct.bestReviews = bestReviewList.map(review => ({
        from: review._retailer.businessName,
        text: review.productComment,
        value: review.productRating,
        createdAt: review.createdAt
    }));
    formattedProduct.reviews = reviewList.map(review => ({
        from: review._retailer.businessName,
        text: review.productComment,
        value: review.productRating,
        createdAt: review.createdAt
    }));
    formattedProduct.vendorReviews = vendorReviewList.map(review => ({
        from: review._retailer.businessName,
        text: review.vendorComment,
        value: review.vendorRating,
        createdAt: review.createdAt
    }));

    ctx.body = {
        data: formattedProduct
    };
}

// Controller: Create/Overwrite a product
async function postProduct(ctx) {
    const { Category, Product, SystemLog } = mongoose.models;
    const payload = pick(
        ctx.request.body,
        'type',
        'sku',
        'name',
        'description',
        'category',
        'strain',
        'status',
        'content',
        'returnPolicy',
        'nonRefundable',
        'mainImage',
        'images',
        'files',
        'effects',
        'medicalSymptoms',
        'medicalConditions',
        'flowers',
        'environment',
        'weights',
        'sizing',
        'custom1',
        'custom2',
        'batches',
        'location',
        'deliveryDays',
        'quantityDiscount',
        'source',
        'metrcPckgID',
        'metrcPckgLabel',
        'metrcSync',
        'metrcSyncDate',
        'mainSku'
    );

    if (!ObjectId.isValid(payload.category)) {
        throw new HttpError(404, 'Category required', { category: 'required' });
    }

    payload._category = await Category.findById(payload.category);
    if (!payload._category) {
        throw new HttpError(404, 'Category not found', { category: 'required' });
    }
    if ('location' in payload) {
        payload._location = payload.location;
        delete payload.location;
    }

    try {
        ctx.state.product = await Product.create(payload, ctx.state.vendor);
    }
    catch (err) {
        if (err.code === 11000) {
            throw new HttpError(422, 'Sku must be unique string', { sku: 'Sku must be unique string' });
        } else if (err.errors.code === 11001) {
            throw new HttpError(422, err.messages, { sku: err.messages });
        }
        throw err;
    }
    SystemLog.vendorProductCreated(ctx, ctx.state.product);

    // Check if this is the vendor's first product and trigger campaign
    try {
        const { Product } = mongoose.models;
        const productCount = await Product.countDocuments({
            _vendor: ctx.state.vendor._id,
            enabled: true
        });

        if (productCount === 1) {
            // This is the first product - trigger first product campaign
            await campaignTriggers.triggerCampaign(campaignTriggers.TRIGGER_EVENTS.FIRST_PRODUCT_LISTED, {
                userId: ctx.state.user._id,
                vendorId: ctx.state.vendor._id,
                productId: ctx.state.product._id,
                productName: ctx.state.product.name
            });
        }
    } catch (error) {
        console.error('Error triggering first product campaign:', error);
        // Don't fail the main request if campaign trigger fails
    }

    ctx.body = {
        data: ctx.state.product.formatSource()
    };
}

// Controller: Update an existing Product
async function patchProduct(ctx) {
    const { Category, SystemLog } = mongoose.models;
    const payload = pick(
        ctx.request.body,
        'type',
        'sku',
        'name',
        'status',
        'description',
        'category',
        'strain',
        'content',
        'returnPolicy',
        'nonRefundable',
        'mainImage',
        'images',
        'files',
        'effects',
        'medicalSymptoms',
        'medicalConditions',
        'flowers',
        'environment',
        'weights',
        'colors',
        'sizing',
        'custom1',
        'custom2',
        'batches',
        'location',
        'deliveryDays',
        'quantityDiscount',
        'source',
        'metrcPckgID',
        'metrcPckgLabel',
        'metrcSync',
        'metrcSyncDate',
        'mainSku'
    );
    if (!ObjectId.isValid(payload.category)) {
        throw new HttpError(404, 'Category required', { category: 'required' });
    }

    payload._category = await Category.findById(payload.category);
    if (!payload._category) {
        throw new HttpError(404, 'Category not found', { category: 'required' });
    }
    const snapshot = SystemLog.takeSnapshot(ctx.state.product);
    if ('location' in payload) {
        payload._location = payload.location;
        delete payload.location;
    }

    try {
        ctx.state.product.set(payload);
        await ctx.state.product.save();
    }
    catch (err) {
        if (err.code === 11000) {
            throw new HttpError(422, 'Sku must be unique string', { sku: 'Sku must be unique string' });
        } else if (err.errors.code === 11001) {
            throw new HttpError(422, err.message, { sku: err.message });
        }
        throw err;
    }

    SystemLog.vendorProductUpdated(ctx, snapshot, ctx.state.product);
    ctx.body = {
        data: ctx.state.product.formatSource()
    };
}

// Controller: Delete a product
async function deleteProduct(ctx) {
    const { SystemLog } = mongoose.models;
    await ctx.state.product.remove();
    SystemLog.vendorProductRemoved(ctx, ctx.state.product);

    ctx.body = {
        data: { ok: true }
    };
}
