'use strict';

const fs = require('fs');
const join = require('path').join;
const sentry = require('../services/sentry');
const db = require('../services/db');
const view = require('../services/view');
const jobs = join(__dirname, 'jobs');
const argv = require('minimist')(process.argv.slice(2));
const queue = require('../services/queue');

let agenda;
let started = false;

const logger = sentry.logger('queue');

view.init(false);
db.connect();
sentry.init();

start();

process.on('SIGTERM', graceful);
process.on('SIGINT', graceful);


async function graceful() {
    await agenda.stop();
    process.exit(0);
}

function start() {
    agenda = queue.init();
    agenda.on('fail', errorHandler);

    fs.readdirSync(jobs)
        .forEach(file => {
            const
                path = join(jobs, file),
                job = require(path);
            job(agenda, argv);
        });

    queue.start();
    started = true;
}

function stop(done) {
    if (!started) {
        return done();
    }
    agenda.stop(function () {
        done();
    });
}

function errorHandler(err, job) {
    logger(err, {
        extra: {
            job: job.name
        }
    });
    console.error(`JOB FAILED ${job.name}: ${err.message}`);
    console.error(err.stack);
}

