'use strict';

const mongoose = require('mongoose');
const { AppError } = require('./error');

// User behavior tracking and analytics
class UserAnalytics {
    constructor() {
        this.behaviorStore = new Map(); // In production, use Redis or database
        this.personaCache = new Map();
    }

    // Track user behavior event
    async trackBehavior(userId, eventType, eventData = {}) {
        try {
            const behaviorKey = `behavior_${userId}`;
            
            if (!this.behaviorStore.has(behaviorKey)) {
                this.behaviorStore.set(behaviorKey, {
                    userId,
                    events: [],
                    sessions: [],
                    createdAt: new Date(),
                    lastActivity: new Date()
                });
            }
            
            const userBehavior = this.behaviorStore.get(behaviorKey);
            
            const event = {
                type: eventType,
                data: eventData,
                timestamp: new Date(),
                sessionId: eventData.sessionId || 'unknown'
            };
            
            userBehavior.events.push(event);
            userBehavior.lastActivity = new Date();
            
            // Keep only last 1000 events per user
            if (userBehavior.events.length > 1000) {
                userBehavior.events = userBehavior.events.slice(-1000);
            }
            
            return event;
            
        } catch (error) {
            console.error('Error tracking user behavior:', error);
            throw error;
        }
    }

    // Get user behavior data
    getUserBehavior(userId) {
        const behaviorKey = `behavior_${userId}`;
        return this.behaviorStore.get(behaviorKey) || null;
    }

    // Analyze user behavior and determine buyer persona
    async analyzeUserPersona(userId) {
        try {
            // Check cache first
            if (this.personaCache.has(userId)) {
                const cached = this.personaCache.get(userId);
                if (Date.now() - cached.timestamp < 3600000) { // 1 hour cache
                    return cached.persona;
                }
            }
            
            const userData = await this.buildUserAnalyticsProfile(userId);
            const behaviorData = this.getUserBehavior(userId);
            
            const persona = this.calculateBuyerPersona(userData, behaviorData);
            
            // Cache the result
            this.personaCache.set(userId, {
                persona,
                timestamp: Date.now()
            });
            
            return persona;
            
        } catch (error) {
            console.error('Error analyzing user persona:', error);
            throw error;
        }
    }

    // Build comprehensive user profile for analysis
    async buildUserAnalyticsProfile(userId) {
        const { User, Vendor, Retailer, Product, Order, Application } = mongoose.models;
        
        try {
            const user = await User.findById(userId).lean();
            if (!user) {
                throw new Error('User not found');
            }
            
            const profile = {
                userId: user._id.toString(),
                email: user.email,
                createdAt: user.createdAt,
                lastLogin: user.lastLogin,
                userRole: 'user',
                demographics: {
                    accountAge: Math.floor((Date.now() - user.createdAt.getTime()) / (1000 * 60 * 60 * 24)), // days
                    loginFrequency: await this.calculateLoginFrequency(userId),
                    timeZone: user.timeZone || 'unknown'
                },
                businessMetrics: {},
                behaviorMetrics: {}
            };
            
            // Vendor-specific data
            const vendor = await Vendor.findOne({ _user: userId }).lean();
            if (vendor) {
                profile.userRole = 'vendor';
                profile.businessMetrics = {
                    businessSize: await this.categorizeBusinessSize(vendor._id, 'vendor'),
                    productCount: await Product.countDocuments({ _vendor: vendor._id, enabled: true }),
                    totalRevenue: await this.calculateTotalRevenue(vendor._id, 'vendor'),
                    averageOrderValue: await this.calculateAverageOrderValue(vendor._id, 'vendor'),
                    isDomestic: vendor.isDomestic,
                    state: vendor.state,
                    hasMetrcIntegration: !!vendor.metrcKey
                };
                
                // Vendor application data
                const vendorApp = await Application.findOne({ _vendor: vendor._id }).lean();
                if (vendorApp) {
                    profile.businessMetrics.applicationStatus = vendorApp.status;
                    profile.businessMetrics.businessType = vendorApp.businessType;
                }
            }
            
            // Retailer-specific data
            const retailer = await Retailer.findOne({ _user: userId }).lean();
            if (retailer) {
                profile.userRole = 'retailer';
                profile.businessMetrics = {
                    businessSize: await this.categorizeBusinessSize(retailer._id, 'retailer'),
                    orderCount: await Order.countDocuments({ _retailer: retailer._id }),
                    totalSpent: await this.calculateTotalRevenue(retailer._id, 'retailer'),
                    averageOrderValue: await this.calculateAverageOrderValue(retailer._id, 'retailer'),
                    isDomestic: retailer.isDomestic,
                    state: retailer.state,
                    budget: retailer.retailerBudget
                };
                
                // Retailer application data
                const retailerApp = await Application.findOne({ _retailer: retailer._id }).lean();
                if (retailerApp) {
                    profile.businessMetrics.applicationStatus = retailerApp.status;
                    profile.businessMetrics.businessType = retailerApp.businessType;
                }
            }
            
            return profile;
            
        } catch (error) {
            console.error('Error building user analytics profile:', error);
            throw error;
        }
    }

    // Calculate buyer persona based on user data and behavior
    calculateBuyerPersona(userData, behaviorData) {
        const persona = {
            primaryPersona: 'unknown',
            confidence: 0,
            characteristics: [],
            conversionLikelihood: 0,
            recommendedActions: [],
            lastAnalyzed: new Date()
        };
        
        if (!userData) {
            return persona;
        }
        
        // Analyze based on user role
        if (userData.userRole === 'vendor') {
            persona.primaryPersona = this.analyzeVendorPersona(userData, behaviorData);
        } else if (userData.userRole === 'retailer') {
            persona.primaryPersona = this.analyzeRetailerPersona(userData, behaviorData);
        }
        
        // Calculate confidence and conversion likelihood
        persona.confidence = this.calculatePersonaConfidence(userData, behaviorData);
        persona.conversionLikelihood = this.calculateConversionLikelihood(userData, behaviorData);
        
        // Generate characteristics and recommendations
        persona.characteristics = this.generatePersonaCharacteristics(userData, behaviorData);
        persona.recommendedActions = this.generateRecommendedActions(persona, userData);
        
        return persona;
    }

    // Analyze vendor persona
    analyzeVendorPersona(userData, behaviorData) {
        const metrics = userData.businessMetrics;
        
        if (!metrics) return 'new_vendor';
        
        // High-volume established vendor
        if (metrics.productCount > 50 && metrics.totalRevenue > 100000) {
            return 'enterprise_vendor';
        }
        
        // Medium-sized growing vendor
        if (metrics.productCount > 10 && metrics.totalRevenue > 10000) {
            return 'growth_vendor';
        }
        
        // Small but active vendor
        if (metrics.productCount > 0 && metrics.totalRevenue > 1000) {
            return 'active_vendor';
        }
        
        // New or inactive vendor
        if (metrics.productCount === 0) {
            return 'new_vendor';
        }
        
        return 'small_vendor';
    }

    // Analyze retailer persona
    analyzeRetailerPersona(userData, behaviorData) {
        const metrics = userData.businessMetrics;
        
        if (!metrics) return 'new_retailer';
        
        // High-volume buyer
        if (metrics.orderCount > 20 && metrics.totalSpent > 50000) {
            return 'enterprise_retailer';
        }
        
        // Regular buyer
        if (metrics.orderCount > 5 && metrics.totalSpent > 5000) {
            return 'regular_retailer';
        }
        
        // Occasional buyer
        if (metrics.orderCount > 0) {
            return 'occasional_retailer';
        }
        
        return 'new_retailer';
    }

    // Calculate persona confidence score
    calculatePersonaConfidence(userData, behaviorData) {
        let confidence = 0.5; // Base confidence
        
        // Increase confidence based on data availability
        if (userData.businessMetrics && Object.keys(userData.businessMetrics).length > 0) {
            confidence += 0.2;
        }
        
        if (behaviorData && behaviorData.events.length > 10) {
            confidence += 0.2;
        }
        
        if (userData.demographics.accountAge > 30) {
            confidence += 0.1;
        }
        
        return Math.min(confidence, 1.0);
    }

    // Calculate conversion likelihood
    calculateConversionLikelihood(userData, behaviorData) {
        let likelihood = 0.3; // Base likelihood
        
        // Factors that increase conversion likelihood
        if (userData.userRole === 'vendor') {
            if (userData.businessMetrics.productCount > 0) likelihood += 0.3;
            if (userData.businessMetrics.applicationStatus === 'approved') likelihood += 0.2;
            if (userData.businessMetrics.hasMetrcIntegration) likelihood += 0.1;
        } else if (userData.userRole === 'retailer') {
            if (userData.businessMetrics.orderCount > 0) likelihood += 0.3;
            if (userData.businessMetrics.applicationStatus === 'approved') likelihood += 0.2;
            if (userData.businessMetrics.budget > 1000) likelihood += 0.1;
        }
        
        // Behavior factors
        if (behaviorData) {
            const recentActivity = behaviorData.events.filter(e => 
                Date.now() - e.timestamp.getTime() < 7 * 24 * 60 * 60 * 1000 // Last 7 days
            );
            
            if (recentActivity.length > 5) likelihood += 0.1;
        }
        
        return Math.min(likelihood, 1.0);
    }

    // Generate persona characteristics
    generatePersonaCharacteristics(userData, behaviorData) {
        const characteristics = [];
        
        if (userData.demographics.accountAge < 7) {
            characteristics.push('new_user');
        } else if (userData.demographics.accountAge > 90) {
            characteristics.push('established_user');
        }
        
        if (userData.businessMetrics) {
            if (userData.businessMetrics.isDomestic) {
                characteristics.push('domestic_business');
            } else {
                characteristics.push('international_business');
            }
            
            if (userData.businessMetrics.businessSize === 'large') {
                characteristics.push('enterprise_level');
            } else if (userData.businessMetrics.businessSize === 'medium') {
                characteristics.push('mid_market');
            } else {
                characteristics.push('small_business');
            }
        }
        
        return characteristics;
    }

    // Generate recommended actions
    generateRecommendedActions(persona, userData) {
        const actions = [];
        
        if (persona.primaryPersona === 'new_vendor') {
            actions.push('onboarding_sequence', 'product_listing_tutorial', 'first_product_incentive');
        } else if (persona.primaryPersona === 'enterprise_vendor') {
            actions.push('bulk_tools_promotion', 'api_integration_offer', 'dedicated_support');
        }
        
        if (persona.primaryPersona === 'new_retailer') {
            actions.push('marketplace_tour', 'first_order_discount', 'vendor_introduction');
        } else if (persona.primaryPersona === 'enterprise_retailer') {
            actions.push('volume_discounts', 'priority_support', 'custom_terms');
        }
        
        if (persona.conversionLikelihood < 0.5) {
            actions.push('re_engagement_campaign', 'feature_highlights', 'success_stories');
        }
        
        return actions;
    }

    // Helper methods
    async calculateLoginFrequency(userId) {
        // This would typically query login logs
        // For now, return a placeholder
        return 'weekly';
    }

    async categorizeBusinessSize(entityId, entityType) {
        // Categorize based on various metrics
        // This is a simplified version
        return 'small'; // small, medium, large
    }

    async calculateTotalRevenue(entityId, entityType) {
        const { Order } = mongoose.models;
        
        try {
            const matchField = entityType === 'vendor' ? '_vendor' : '_retailer';
            const result = await Order.aggregate([
                { $match: { [matchField]: entityId, status: 'completed' } },
                { $group: { _id: null, total: { $sum: '$total' } } }
            ]);
            
            return result.length > 0 ? result[0].total : 0;
        } catch (error) {
            console.error('Error calculating total revenue:', error);
            return 0;
        }
    }

    async calculateAverageOrderValue(entityId, entityType) {
        const { Order } = mongoose.models;
        
        try {
            const matchField = entityType === 'vendor' ? '_vendor' : '_retailer';
            const result = await Order.aggregate([
                { $match: { [matchField]: entityId, status: 'completed' } },
                { $group: { _id: null, avg: { $avg: '$total' }, count: { $sum: 1 } } }
            ]);
            
            return result.length > 0 ? result[0].avg : 0;
        } catch (error) {
            console.error('Error calculating average order value:', error);
            return 0;
        }
    }

    // Get analytics for multiple users
    async getBulkUserAnalytics(userIds) {
        const results = [];
        
        for (const userId of userIds) {
            try {
                const persona = await this.analyzeUserPersona(userId);
                results.push({ userId, persona });
            } catch (error) {
                console.error(`Error analyzing persona for user ${userId}:`, error);
                results.push({ userId, error: error.message });
            }
        }
        
        return results;
    }

    // Get persona statistics
    getPersonaStatistics() {
        const stats = {
            totalUsers: this.personaCache.size,
            personaDistribution: {},
            averageConfidence: 0,
            averageConversionLikelihood: 0
        };
        
        let totalConfidence = 0;
        let totalConversion = 0;
        
        for (const [userId, cached] of this.personaCache.entries()) {
            const persona = cached.persona;
            
            // Count persona distribution
            stats.personaDistribution[persona.primaryPersona] = 
                (stats.personaDistribution[persona.primaryPersona] || 0) + 1;
            
            totalConfidence += persona.confidence;
            totalConversion += persona.conversionLikelihood;
        }
        
        if (stats.totalUsers > 0) {
            stats.averageConfidence = totalConfidence / stats.totalUsers;
            stats.averageConversionLikelihood = totalConversion / stats.totalUsers;
        }
        
        return stats;
    }
}

// Export singleton instance
const userAnalytics = new UserAnalytics();

module.exports = userAnalytics;
