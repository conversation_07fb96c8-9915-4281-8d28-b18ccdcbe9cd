'use strict';

const config = require('../../services/config');
const mongoose = require('mongoose');
const email = require('../../services/email');
const constants = require('../../models/constants');
const { HttpError } = require('../../services/error');
const uuidv4 = require('uuid/v4');
const Router = require('koa-router');
const campaignTriggers = require('../../services/campaign-triggers');

module.exports = Router()
    .get('/', getIndex)
    .get('/login', getLogin)
    .get('/retailer/login', ctx => ctx.redirect('/login'))
    .get('/vendor/login', ctx => ctx.redirect('/login'))
    .post('/login', postLogin)
    .post('/logout', postLogout)
    .get('/signup', getSignup)
    .get('/retailer/signup', ctx => ctx.redirect('/signup/buyer'))
    .get('/vendor/signup', ctx => ctx.redirect('/signup/seller'))
    .get('/signup/buyer', getSignup)
    .get('/signup/seller', getSignup)
    .post('/signup', postSignup)    
    .get('/restore/:token', getRestoreToken)
    .get('/restore/:token/expired', getRestoreTokenExpired)
    .post('/restore/:token', postRestoreToken)
    .get('/restore', getRestore)
    .post('/restore', postRestore)
    .post('/verify-email', postVerifyEmail)
    .get('/verify-email', getVerifyEmailToken)
    .get('/invite/:token', getInvite)
    .post('/invite/:token', postInvite)
    .get('/login/*', ctx => ctx.redirect('/login'))
    .get('/restore/*', ctx => ctx.redirect('/restore'))
    .get('/signup/*', ctx => ctx.redirect('/signup'))
    .get('/*', ctx => ctx.redirect('/login'));

async function getIndex(ctx) {
    if (config.app.env === constants.ENV.PRODUCTION) {
        return ctx.redirect('https://canideal.com');
    }
    ctx.body = ctx.render('site/index/index.html');
}

async function getLogin(ctx) {
    ctx.body = ctx.render('site/user/index.html');
}

async function postLogin(ctx) {
    const { User } = mongoose.models;
    const payload = ctx.request.body;

    let user = await User
        .findOne({ email: payload.email })
        .populate('_retailer _vendor')
        .exec();

    if (!user) {
        throw new HttpError(422, 'The username and password you entered did not match our records. Please double-check and try again.');
    }

    if (!user.comparePassword(payload.password)) {
        throw new HttpError(422, 'The username and password you entered did not match our records. Please double-check and try again.', { password: 'incorrect' });
    }
    if (user.status !== constants.USER.STATUS.ACTIVE) {
        throw new HttpError(422, 'Inactive account, please contact to support');
    }
    const { _vendor: vendor, _retailer: retailer } = user;

    if (!vendor.enabled && !retailer.enabled) {
        throw new HttpError(422, 'Inactive account, please contact to support');
    }

    let redirectUrl;
    if (vendor.enabled && vendor.hasUserAccess(user)) {
        redirectUrl = '/vendor';
    }
    else if (retailer.enabled && retailer.hasUserAccess(user)) {
        redirectUrl = '/retailer';
    }
    else {
        throw new HttpError(422, 'Inactive account, please contact to support');
    }

    ctx.session.userId = String(user._id);
    user.updateLogged(ctx.request);


    if (ctx.session.redirectUrl) {
        redirectUrl = ctx.session.redirectUrl;
        delete ctx.session.redirectUrl;
    }
    ctx.body = {
        ok: true,
        user: user.formatSource(),
        redirectUrl
    };
}

async function postLogout(ctx) {
    delete ctx.session.userId;
    if (ctx.session.shadowLogin) {
        ctx.session.shadowLogin = false;
    }
    return ctx.redirect('/login');
}

async function getSignup(ctx) {
    const { redirectUrl } = ctx.request.query;
    if (redirectUrl) {
        ctx.session.redirectUrl = redirectUrl;
    }
    ctx.body = ctx.render('site/user/index.html');
}

async function postSignup(ctx) {
    const { User, SystemLog } = mongoose.models;
    const { email, firstName, lastName, businessName, phone, isBroker, createRetailer, createVendor, retailerBudget, vendorProductCount, referral, utm } = ctx.request.body;
    if (!createRetailer && !createVendor) {
        throw new HttpError(422, 'Account type required');
    }
    let user = await User.create({ email, firstName, lastName, businessName, phone, isBroker, createRetailer, createVendor, state: ctx.state.currentState, retailerBudget, vendorProductCount, referral, utm });
    user.updateLogged(ctx.request);
    let redirectUrl;
    if (createVendor) {
        redirectUrl = '/vendor';
    }
    else if (createRetailer) {
        redirectUrl = '/retailer';
    }
    ctx.session.userId = String(user._id);
    if (['/vendor', '/retailer'].includes(ctx.session.redirectUrl)) {
        delete ctx.session.redirectUrl;
    }
    if (ctx.session.redirectUrl) {
        redirectUrl = ctx.session.redirectUrl;
        delete ctx.session.redirectUrl;
    }
    SystemLog.userCreated(ctx, user);

    // Trigger HubSpot campaigns for user signup
    try {
        // General user signup trigger
        await campaignTriggers.triggerCampaign(campaignTriggers.TRIGGER_EVENTS.USER_SIGNUP, {
            userId: user._id,
            email: user.email,
            userRole: createVendor ? 'vendor' : 'retailer'
        });

        // Role-specific signup triggers
        if (createVendor) {
            await campaignTriggers.triggerCampaign(campaignTriggers.TRIGGER_EVENTS.VENDOR_SIGNUP, {
                userId: user._id,
                vendorId: user._vendor,
                storeName: businessName,
                isDomestic: ctx.state.isDomestic
            });
        }

        if (createRetailer) {
            await campaignTriggers.triggerCampaign(campaignTriggers.TRIGGER_EVENTS.RETAILER_SIGNUP, {
                userId: user._id,
                retailerId: user._retailer,
                businessName,
                isDomestic: ctx.state.isDomestic
            });
        }
    } catch (error) {
        console.error('Error triggering signup campaigns:', error);
        // Don't fail the main request if campaign trigger fails
    }

    ctx.body = {
        ok: true,
        user: user.formatSource(),
        redirectUrl
    };
}

async function getRestore(ctx) {
    ctx.body = ctx.render('site/user/index.html');
}

async function postRestore(ctx) {
    const { User } = mongoose.models;
    const payload = ctx.request.body;

    if (typeof payload.email !== 'string' || !payload.email.length) {
        throw new HttpError(422, 'Email field is required');
    }

    let user = await User.findOne({ email: payload.email });

    if (!user) {
        throw new HttpError(422, 'User not found');
    }

    const restorePasswordToken = uuidv4();
    user.set({ restorePasswordToken, restorePasswordTokenCreatedAt: new Date() });
    await user.save();
    await email.create.restorePasswordEmail({ user });

    ctx.body = {
        ok: true
    };
}

async function getRestoreTokenExpired(ctx) {
    ctx.body = ctx.render('site/user/index.html');
}

async function getRestoreToken(ctx) {
    const { User } = mongoose.models;

    let user = await User.findOne({
        restorePasswordToken: ctx.params.token
    });

    if (!user) {
        return ctx.redirect('/restore');
    }

    if (user.restorePasswordTokenCreatedAt) {
        const isExpired = (new Date().getTime() - new Date(user.restorePasswordTokenCreatedAt).getTime()) > 1 * 60 * 60 * 1000;

        if (isExpired) {
            return ctx.redirect(`/restore/${user.restorePasswordToken}/expired`);
        }
    }

    ctx.state.serverData = {
        token: user.restorePasswordToken
    };
    ctx.body = ctx.render('site/user/index.html');
}

async function postRestoreToken(ctx) {
    const { User } = mongoose.models;
    const payload = ctx.request.body;

    let user = await User.findOne({
        restorePasswordToken: ctx.params.token
    }).populate('_retailer _vendor');

    if (!user) {
        throw new HttpError(404, 'Link is incorrect');
    }

    user.restorePasswordToken = null;
    await user.updatePassword(payload.password);
    user.updateLogged(ctx.request);

    ctx.session.userId = String(user._id);

    const { _vendor: vendor, _retailer: retailer } = user;
    let redirectUrl;
    if (user.lastLoginBy === constants.USER.ROLE.VENDOR && vendor.enabled) {
        redirectUrl = '/vendor';
    }
    else if (retailer.enabled) {
        redirectUrl = '/retailer';
    }

    if (ctx.session.redirectUrl) {
        redirectUrl = ctx.session.redirectUrl;
        delete ctx.session.redirectUrl;
    }
    ctx.body = {
        ok: true,
        redirectUrl
    };
}

async function postVerifyEmail(ctx) {
    const { user } = ctx.state;

    user.sendVerifyEmail();

    ctx.response.status = 204;
}

async function getVerifyEmailToken(ctx) {
    const { User } = mongoose.models;
    let user = await User.findOne({
        verifyEmailToken: ctx.query.token
    });

    if (!user) {
        return ctx.redirect('/');
    }
    user.set({
        emailVerified: true,
        verifyEmailToken: undefined
    });
    await user.save();

    return ctx.redirect(`/${user.lastLoginBy || constants.USER.ROLE.RETAILER}`);
}

async function getInvite(ctx) {
    const { Retailer, Vendor } = mongoose.models;
    const { token } = ctx.params;

    const retailer = await Retailer.findOne({ 'team.status': constants.TEAM.USER_STATUS.INVITED, 'team.token': token });
    const vendor = await Vendor.findOne({ 'team.status': constants.TEAM.USER_STATUS.INVITED, 'team.token': token });

    let team = [];
    if (retailer) {
        team = retailer.team;
    }
    else if (vendor) {
        team = vendor.team;
    }
    else {
        return ctx.redirect('/');
    }
    const newMember = team.find(member => member.token === token);

    if (!newMember) {
        return ctx.redirect('/');
    }
    const isToVendor = newMember.inviteTo === constants.USER.ROLE.VENDOR;

    ctx.state.serverData = {
        inviteTo: isToVendor ? vendor.formatPublic() : retailer.formatPublic()
    };
    ctx.body = ctx.render('site/user/index.html');
}
async function postInvite(ctx) {
    const { User, Retailer, Vendor, SystemLog } = mongoose.models;
    const { token } = ctx.params;

    const retailer = await Retailer.findOne({ 'team.status': constants.TEAM.USER_STATUS.INVITED, 'team.token': token });
    const vendor = await Vendor.findOne({ 'team.status': constants.TEAM.USER_STATUS.INVITED, 'team.token': token });

    let team = [];
    if (retailer) {
        team = retailer.team;
    }
    else if (vendor) {
        team = vendor.team;
    }
    else {
        return ctx.redirect('/');
    }
    const newMember = team.find(member => member.token === token);

    if (!newMember) {
        return ctx.redirect('/');
    }
    const isToVendor = newMember.inviteTo === constants.USER.ROLE.VENDOR;
    const inviteFromUser = await User.findById(newMember._sentBy);
    let user;
    if (newMember._user) {
        user = await User.findById(newMember._user);
    }
    else {
        user = await User.getUserByInvite({
            email: newMember.email,
            firstName: newMember.firstName,
            lastName: newMember.lastName,
            inviteTo: newMember.inviteTo,
            inviteFrom: inviteFromUser.name
        });
        newMember._user = user._id;
    }
    user.set({
        _retailer: isToVendor ? vendor._retailer._id : retailer._id,
        _vendor: isToVendor ? vendor._id : retailer._vendor._id
    });
    await user.save({ timestamps: false });


    newMember.status = constants.TEAM.USER_STATUS.ACTIVE;
    newMember.token = undefined;
    retailer.set({ team });
    vendor.set({ team });
    await retailer.save({ timestamps: false, validateBeforeSave: false });
    await vendor.save({ timestamps: false, validateBeforeSave: false });

    ctx.flash = { modal: `You accept invite to ${isToVendor ? vendor.storeName : retailer.businessName}` };
    ctx.session.userId = String(user._id);

    user.updateLogged(ctx.request);
    SystemLog.userCreatedByInvite(ctx, user, inviteFromUser, isToVendor ? constants.USER.ROLE.VENDOR : constants.USER.ROLE.RETAILER);
    return ctx.body = {
        data: {
            ok: true,
            redirectUrl: isToVendor ? '/vendor' : '/retailer'
        }
    };
}
