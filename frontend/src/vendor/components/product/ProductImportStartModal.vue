<template>
  <div class="modal-content">
      <div class="modal-header">
        <button type="button" class="close" @click="$emit('close')">
          <span aria-hidden="true">&times;</span>
        </button>
        <h4 class="modal-title">Import Products</h4>
      </div>
      <div class="modal-body">
        <div v-if="step === 'start'">
          <div class="alert alert-info mb-4">
            <div class="d-flex">
              <div class="mr-3">
                <svg class="icon icon-lg" viewBox="0 0 24 24">
                  <path d="M11 17H13V11H11V17ZM12 2C6.48 2 2 6.48 2 12S6.48 22 12 22 22 17.52 22 12 17.52 2 12 2ZM12 20C7.59 20 4 16.41 4 12S7.59 4 12 4 20 7.59 20 12 16.41 20 12 20ZM11 9H13V7H11V9Z" fill-rule="evenodd"></path>
                </svg>
              </div>
              <div>
                <p>To import products, you need to use our template file.</p>
                <p class="mb-0">Have you already downloaded the template file?</p>
              </div>
            </div>
          </div>

          <div class="text-center mb-4">
            <div class="mb-3">
              <div class="btn-group">
                <button type="button" class="btn btn-primary" @click="downloadTemplate('xlsx')">
                  <svg class="icon mr-1" viewBox="0 0 24 24">
                    <path d="M19 9h-4V3H9v6H5l7 7 7-7zM5 18v2h14v-2H5z" fill-rule="evenodd"></path>
                  </svg>
                  Download Excel Template
                </button>
                <button type="button" class="btn btn-outline-primary" @click="downloadTemplate('csv')">
                  <svg class="icon mr-1" viewBox="0 0 24 24">
                    <path d="M19 9h-4V3H9v6H5l7 7 7-7zM5 18v2h14v-2H5z" fill-rule="evenodd"></path>
                  </svg>
                  Download CSV Template
                </button>
              </div>
            </div>
            <button type="button" class="btn btn-success" @click="step = 'upload'">
              I already have the template
            </button>
          </div>
        </div>

        <div v-if="step === 'upload'">
          <div class="alert alert-info mb-4">
            <div class="d-flex">
              <div class="mr-3">
                <svg class="icon icon-lg" viewBox="0 0 24 24">
                  <path d="M11 17H13V11H11V17ZM12 2C6.48 2 2 6.48 2 12S6.48 22 12 22 22 17.52 22 12 17.52 2 12 2ZM12 20C7.59 20 4 16.41 4 12S7.59 4 12 4 20 7.59 20 12 16.41 20 12 20ZM11 9H13V7H11V9Z" fill-rule="evenodd"></path>
                </svg>
              </div>
              <div>
                <p>Upload a spreadsheet with your product information. The file should be in CSV or Excel format.</p>
                <p class="mb-0">
                  <div class="btn-group">
                    <a href="#" @click.prevent="downloadTemplate('xlsx')" class="btn btn-sm btn-outline-primary">
                      <svg class="icon mr-1" viewBox="0 0 24 24">
                        <path d="M19 9h-4V3H9v6H5l7 7 7-7zM5 18v2h14v-2H5z" fill-rule="evenodd"></path>
                      </svg>
                      Excel Template
                    </a>
                    <a href="#" @click.prevent="downloadTemplate('csv')" class="btn btn-sm btn-outline-primary">
                      <svg class="icon mr-1" viewBox="0 0 24 24">
                        <path d="M19 9h-4V3H9v6H5l7 7 7-7zM5 18v2h14v-2H5z" fill-rule="evenodd"></path>
                      </svg>
                      CSV Template
                    </a>
                  </div>
                </p>
              </div>
            </div>
          </div>

          <div
            class="text-center p-5 border border-dashed rounded mb-4"
            @dragover.prevent="onDragOver"
            @dragleave.prevent="onDragLeave"
            @drop.prevent="onDrop"
            :class="{ 'bg-light': isDragging }"
          >
            <div v-if="!file">
              <div class="mb-3">
                <svg class="icon icon-2x text-muted" viewBox="0 0 24 24">
                  <path d="M19 9h-4V3H9v6H5l7 7 7-7zM5 18v2h14v-2H5z" fill-rule="evenodd"></path>
                </svg>
              </div>
              <p class="mb-3">Drag and drop your file here</p>
              <p class="mb-3">- or -</p>
              <label class="btn btn-primary mb-3">
                Select Spreadsheet File
                <input
                  type="file"
                  accept=".csv,.xlsx,.xls"
                  @change="onFileSelected"
                  class="d-none"
                />
              </label>
              <p class="text-muted">Supported formats: .csv, .xlsx, .xls</p>
              <p class="text-muted">Please use the template file for best results</p>
            </div>
            <div v-else>
              <div class="d-flex align-items-center justify-content-center">
                <div class="mr-3">
                  <svg class="icon icon-2x" viewBox="0 0 24 24">
                    <path d="M14 2H6c-1.1 0-1.99.9-1.99 2L4 20c0 1.1.89 2 1.99 2H18c1.1 0 2-.9 2-2V8l-6-6zm2 16H8v-2h8v2zm0-4H8v-2h8v2zm-3-5V3.5L18.5 9H13z" fill-rule="evenodd"></path>
                  </svg>
                </div>
                <div class="flex-grow-1 text-left">
                  <div>{{ file.name }}</div>
                  <div class="text-muted">{{ formatFileSize(file.size) }}</div>
                </div>
                <div>
                  <button type="button" class="btn btn-sm btn-outline-danger" @click="removeFile">
                    <svg class="icon" viewBox="0 0 24 24">
                      <path d="M19 6.41L17.59 5 12 10.59 6.41 5 5 6.41 10.59 12 5 17.59 6.41 19 12 13.41 17.59 19 19 17.59 13.41 12z" fill-rule="evenodd"></path>
                    </svg>
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>

        <div v-if="step === 'validate'" class="text-center">
          <div v-if="isValidating">
            <div class="spinner-border text-primary mb-3" role="status">
              <span class="sr-only">Validating...</span>
            </div>
            <p>Validating your spreadsheet...</p>
          </div>
          <div v-else-if="validationErrors.length">
            <div class="alert alert-danger">
              <h5>Validation Errors</h5>
              <ul class="text-left mb-0">
                <li v-for="(error, index) in validationErrors" :key="index">{{ error }}</li>
              </ul>
              <div class="mt-3">
                <p><strong>Common issues:</strong></p>
                <ul class="text-left">
                  <li>Make sure you're using the template file without modifying the column names</li>
                  <li>The template contains example data - you need to replace it with your own products</li>
                  <li>Required columns in the Products sheet: SKU, Name, Category, Type</li>
                  <li>Required columns in the Variants sheet: Product SKU, Variant SKU, Price, Quantity</li>
                  <li>Required columns in the Batches sheet: Product SKU, Batch ID, Batch Name</li>
                  <li>Make sure the file is not empty and contains at least one product</li>
                </ul>
                <p class="mt-3"><strong>How to use the template:</strong></p>
                <ol class="text-left">
                  <li>Download the template using the button below</li>
                  <li>Open the template in Excel or another spreadsheet program</li>
                  <li>Replace the example data (PROD001, PROD002, etc.) with your own product data</li>
                  <li>Save the file and upload it here</li>
                </ol>
                <p class="mt-2">
                  <div class="btn-group">
                    <button type="button" class="btn btn-sm btn-primary" @click="downloadTemplate('xlsx')">
                      <svg class="icon mr-1" viewBox="0 0 24 24">
                        <path d="M19 9h-4V3H9v6H5l7 7 7-7zM5 18v2h14v-2H5z" fill-rule="evenodd"></path>
                      </svg>
                      Excel Template
                    </button>
                    <button type="button" class="btn btn-sm btn-outline-primary" @click="downloadTemplate('csv')">
                      <svg class="icon mr-1" viewBox="0 0 24 24">
                        <path d="M19 9h-4V3H9v6H5l7 7 7-7zM5 18v2h14v-2H5z" fill-rule="evenodd"></path>
                      </svg>
                      CSV Template
                    </button>
                  </div>
                </p>
              </div>
            </div>
          </div>
          <div v-else>
            <div class="alert alert-success">
              <svg class="icon icon-lg mr-2" viewBox="0 0 24 24">
                <path d="M9 16.17L4.83 12l-1.42 1.41L9 19 21 7l-1.41-1.41z" fill-rule="evenodd"></path>
              </svg>
              Validation successful!
            </div>
            <div class="text-left">
              <h5>Summary:</h5>
              <ul>
                <li>New products to create: {{ summary.newProducts }}</li>
                <li>Existing products to update: {{ summary.updateProducts }}</li>
                <li>Total variants: {{ summary.totalVariants }}</li>
              </ul>
            </div>
          </div>
        </div>

        <div v-if="step === 'import'" class="text-center">
          <div v-if="isImporting">
            <div class="spinner-border text-primary mb-3" role="status">
              <span class="sr-only">Importing...</span>
            </div>
            <p>Importing your products...</p>
            <div class="progress">
              <div class="progress-bar" :style="{width: importProgress + '%'}"></div>
            </div>
            <p>{{ importedCount }} of {{ totalToImport }} products processed</p>
          </div>
          <div v-else-if="importErrors.length">
            <div class="alert alert-warning">
              <h5>Import Completed with Warnings</h5>
              <p>{{ successCount }} products were imported successfully.</p>
              <p>{{ importErrors.length }} products had errors:</p>
              <ul class="text-left">
                <li v-for="(error, index) in importErrors" :key="index">{{ error }}</li>
              </ul>
            </div>
          </div>
          <div v-else>
            <div class="alert alert-success">
              <svg class="icon icon-lg mr-2" viewBox="0 0 24 24">
                <path d="M9 16.17L4.83 12l-1.42 1.41L9 19 21 7l-1.41-1.41z" fill-rule="evenodd"></path>
              </svg>
              Import completed successfully!
            </div>
            <p>{{ successCount }} products were imported.</p>
          </div>
        </div>
      </div>
      <div class="modal-footer">
        <button type="button" class="btn btn-default" @click="$emit('close')">
          {{ step === 'import' && !isImporting ? 'Close' : 'Cancel' }}
        </button>
        <button
          v-if="step === 'upload'"
          type="button"
          class="btn btn-primary"
          :disabled="!file"
          @click="validateFile"
        >
          Validate
        </button>
        <button
          v-if="step === 'validate' && !isValidating && !validationErrors.length"
          type="button"
          class="btn btn-success"
          @click="importProducts"
        >
          Import Products
        </button>
        <button
          v-if="step === 'validate' && validationErrors.length"
          type="button"
          class="btn btn-primary"
          @click="step = 'upload'"
        >
          Back to Upload
        </button>
      </div>
    </div>
</template>

<script>
import { saveAs } from 'file-saver';

export default {
  name: 'ProductImportStartModal',
  inject: ['$productService'],
  data() {
    return {
      step: 'start', // start, upload, validate, import
      file: null,
      isDragging: false,
      isValidating: false,
      isImporting: false,
      validationErrors: [],
      importErrors: [],
      importProgress: 0,
      importedCount: 0,
      totalToImport: 0,
      successCount: 0,
      summary: {
        newProducts: 0,
        updateProducts: 0,
        totalVariants: 0
      }
    };
  },
  mounted() {
    console.log('ProductImportStartModal mounted');
    // No need to manually show the modal, it's handled by the Modal component
  },
  methods: {
    downloadTemplate(format = 'xlsx') {
      this.$productService.getImportTemplate(format)
        .then(response => {
          const blob = new Blob([response], { type: response.type });
          const filename = `product_import_template.${format}`;
          saveAs(blob, filename);

          // After downloading, move to the upload step
          this.step = 'upload';
        })
        .catch(error => {
          this.$flashError('Error downloading template: ' + error.message);
        });
    },
    onDragOver(event) {
      this.isDragging = true;
    },
    onDragLeave(event) {
      this.isDragging = false;
    },
    onDrop(event) {
      this.isDragging = false;

      if (event.dataTransfer.files.length) {
        const file = event.dataTransfer.files[0];
        this.handleFile(file);
      }
    },
    onFileSelected(event) {
      const file = event.target.files[0];
      if (file) {
        this.handleFile(file);
      }
    },
    handleFile(file) {
      // Check if it's a valid file type
      const validTypes = [
        'text/csv',
        'application/vnd.ms-excel',
        'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
      ];

      // Some browsers might return a generic mime type
      const validExtensions = ['.csv', '.xlsx', '.xls'];
      const fileExtension = file.name.substring(file.name.lastIndexOf('.')).toLowerCase();

      if (!validTypes.includes(file.type) && !validExtensions.includes(fileExtension)) {
        this.$flashError('Please upload a CSV or Excel file.');
        return;
      }

      this.file = file;
    },
    removeFile() {
      this.file = null;
    },
    formatFileSize(bytes) {
      if (bytes === 0) return '0 Bytes';
      const k = 1024;
      const sizes = ['Bytes', 'KB', 'MB', 'GB'];
      const i = Math.floor(Math.log(bytes) / Math.log(k));
      return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    },
    validateFile() {
      this.step = 'validate';
      this.isValidating = true;
      this.validationErrors = [];

      // Use the file directly from the file input
      const fileToUpload = this.file;

      // Check if the file is empty or not provided
      if (!fileToUpload || fileToUpload.size === 0) {
        this.isValidating = false;
        this.validationErrors = ['The file is empty. Please download and use the template file.'];
        return;
      }

      this.$productService.validateImportFile(fileToUpload)
        .then(response => {
          this.isValidating = false;
          console.log('Validation response:', response);

          if (response.data && response.data.data) {
            const { valid, errors, summary, isTemplateValid } = response.data.data;
            console.log('Validation data:', { valid, errors, summary, isTemplateValid });

            // Check if the template is valid
            if (isTemplateValid === false) {
              this.validationErrors = ['The file does not match the expected template format. Please download and use the template file.'];
              return;
            }

            if (valid) {
              this.summary = summary;
            } else {
              this.validationErrors = errors || ['Unknown validation error'];
            }
          } else {
            console.error('Invalid response structure:', response);
            this.validationErrors = ['Invalid response from server. Please check the browser console for details.'];
          }
        })
        .catch(error => {
          this.isValidating = false;
          console.error('Validation error:', error);

          // Check if there's a response with error details
          if (error.response && error.response.data) {
            if (error.response.data.message) {
              this.validationErrors = [error.response.data.message];
            } else if (error.response.data.error) {
              this.validationErrors = [error.response.data.error];
            } else {
              this.validationErrors = ['Error validating file: ' + (error.message || 'Unknown error')];
            }
          } else {
            this.validationErrors = ['Error validating file: ' + (error.message || 'Unknown error')];
          }
        });
    },
    importProducts() {
      this.step = 'import';
      this.isImporting = true;
      this.importErrors = [];
      this.importProgress = 0;
      this.importedCount = 0;
      this.totalToImport = this.summary.newProducts + this.summary.updateProducts;

      // Use the file directly from the file input
      const fileToUpload = this.file;

      this.$productService.importProducts(fileToUpload)
        .then(response => {
          this.isImporting = false;

          if (response.data && response.data.data) {
            const { success, errors, warnings } = response.data.data;

            this.successCount = success ? success.length : 0;

            // Process errors
            this.importErrors = [];
            if (errors && errors.length > 0) {
              this.importErrors = errors.map(err => `Product ${err.sku}: ${err.error}`);
            }

            // Process warnings
            if (warnings && warnings.length > 0) {
              warnings.forEach(warning => {
                this.importErrors.push(`Warning - Product ${warning.sku}: ${warning.warning}`);
              });
            }

            // Set progress to 100%
            this.importProgress = 100;
            this.importedCount = this.totalToImport;

            // Refresh the product list
            this.$emit('import-complete');
          } else {
            this.importErrors = ['Invalid response from server'];
          }
        })
        .catch(error => {
          this.isImporting = false;
          this.importErrors = [error.message || 'Error importing products'];
        });
    }
  }
};
</script>

<style scoped>
.border-dashed {
  border-style: dashed !important;
}
</style>
