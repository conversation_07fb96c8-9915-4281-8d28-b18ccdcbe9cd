'use strict';

// CanIDeal Platform Knowledge Base for AI Assistant
const PLATFORM_KNOWLEDGE = {
    // General Platform Information
    platform: {
        name: 'CanIDeal',
        description: 'B2B cannabis marketplace connecting vendors with retailers',
        purpose: 'Facilitate wholesale cannabis transactions between licensed businesses',
        userTypes: ['vendors', 'retailers', 'admins']
    },

    // User Roles and Capabilities
    roles: {
        vendor: {
            description: 'Cannabis suppliers who list and sell products',
            capabilities: [
                'List products and manage inventory',
                'Receive and fulfill orders from retailers',
                'Manage business profile and settings',
                'Upload product images and descriptions',
                'Set pricing and availability',
                'Communicate with retailers through tickets',
                'View sales analytics and reports',
                'Manage Metrc integration (for domestic vendors)',
                'Import products via spreadsheet upload'
            ],
            commonTasks: [
                'Adding new products',
                'Updating inventory levels',
                'Processing orders',
                'Changing account settings',
                'Uploading product images',
                'Managing business information'
            ],
            dashboardSections: [
                'Dashboard (overview)',
                'Products (inventory management)',
                'Orders (order fulfillment)',
                'Tickets (customer communication)',
                'Settings (account management)'
            ]
        },
        retailer: {
            description: 'Cannabis retailers who purchase products for resale',
            capabilities: [
                'Browse and search vendor products',
                'Place orders with vendors',
                'Manage business profile and settings',
                'Track order history and status',
                'Communicate with vendors through tickets',
                'Set purchasing preferences',
                'Manage payment methods',
                'View spending analytics'
            ],
            commonTasks: [
                'Searching for products',
                'Placing orders',
                'Tracking deliveries',
                'Managing account settings',
                'Contacting vendors',
                'Updating business information'
            ],
            dashboardSections: [
                'Dashboard (overview)',
                'Products (product catalog)',
                'Orders (purchase history)',
                'Tickets (vendor communication)',
                'Settings (account management)'
            ]
        },
        admin: {
            description: 'Platform administrators who manage the marketplace',
            capabilities: [
                'Manage user accounts and applications',
                'Review and approve vendor/retailer applications',
                'Monitor platform activity and analytics',
                'Manage email campaigns and communications',
                'Handle customer support and disputes',
                'Configure platform settings',
                'Export data and generate reports',
                'Manage regulatory compliance'
            ],
            commonTasks: [
                'Reviewing applications',
                'Managing user accounts',
                'Sending email campaigns',
                'Generating reports',
                'Handling support tickets',
                'Platform configuration'
            ],
            dashboardSections: [
                'Dashboard (overview)',
                'Users (account management)',
                'Applications (approval workflow)',
                'Orders (transaction monitoring)',
                'Email Blasts (marketing campaigns)',
                'Analytics (platform metrics)'
            ]
        }
    },

    // Common User Tasks and How-To Guides
    commonTasks: {
        changePassword: {
            steps: [
                'Go to Settings or Account section',
                'Look for "Change Password" or "Security" option',
                'Enter current password',
                'Enter new password twice to confirm',
                'Click "Update Password" or "Save"'
            ],
            location: 'Settings > Account > Security'
        },
        updateProfile: {
            steps: [
                'Navigate to Settings or Profile section',
                'Click "Edit Profile" or similar button',
                'Update the desired information',
                'Save changes by clicking "Update" or "Save"'
            ],
            location: 'Settings > Profile'
        },
        contactSupport: {
            steps: [
                'Look for "Support" or "Help" in the main navigation',
                'Click "Create Ticket" or "Contact Support"',
                'Fill out the support form with your issue',
                'Submit the ticket and wait for response'
            ],
            location: 'Support > Create Ticket'
        },
        addProduct: {
            role: 'vendor',
            steps: [
                'Go to Products section',
                'Click "Add Product" or "+" button',
                'Fill in product details (name, description, price)',
                'Upload product images',
                'Set inventory levels and availability',
                'Save the product'
            ],
            location: 'Products > Add Product'
        },
        placeOrder: {
            role: 'retailer',
            steps: [
                'Browse products or search for specific items',
                'Click on product to view details',
                'Select quantity and add to cart',
                'Review cart and proceed to checkout',
                'Confirm order details and submit'
            ],
            location: 'Products > Product Details > Add to Cart'
        },
        importProducts: {
            role: 'vendor',
            steps: [
                'Go to Products section',
                'Look for "Import" or "Upload" button',
                'Download the template spreadsheet',
                'Fill in product information in the template',
                'Upload the completed spreadsheet',
                'Review and confirm the import'
            ],
            location: 'Products > Import Products'
        }
    },

    // Troubleshooting Common Issues
    troubleshooting: {
        loginIssues: [
            'Check if email and password are correct',
            'Try resetting password if forgotten',
            'Clear browser cache and cookies',
            'Try a different browser or incognito mode',
            'Contact support if issues persist'
        ],
        uploadIssues: [
            'Check file size (usually max 10MB per image)',
            'Ensure file format is supported (JPG, PNG, PDF)',
            'Try a different browser',
            'Check internet connection',
            'Contact support for persistent issues'
        ],
        orderIssues: [
            'Check if all required fields are filled',
            'Verify payment method is valid',
            'Ensure sufficient account balance/credit',
            'Contact vendor directly through tickets',
            'Reach out to support for assistance'
        ]
    },

    // Platform Features and Integrations
    features: {
        metrcIntegration: {
            description: 'Integration with Metrc for compliance tracking',
            applicableTo: 'domestic vendors',
            purpose: 'Track cannabis products for regulatory compliance',
            setup: 'Settings > Metrc Integration'
        },
        ticketSystem: {
            description: 'Communication system between vendors and retailers',
            purpose: 'Handle customer service, order issues, and general communication',
            location: 'Tickets section in dashboard'
        },
        emailNotifications: {
            description: 'Automated email notifications for important events',
            types: ['Order confirmations', 'Application updates', 'Ticket responses'],
            settings: 'Settings > Notifications'
        },
        spreadsheetImport: {
            description: 'Bulk product import via Excel/CSV files',
            applicableTo: 'vendors',
            supportedFormats: ['XLSX', 'CSV'],
            location: 'Products > Import'
        }
    }
};

// Helper functions to retrieve knowledge
function getKnowledgeForRole(role) {
    return PLATFORM_KNOWLEDGE.roles[role] || null;
}

function getCommonTask(taskName) {
    return PLATFORM_KNOWLEDGE.commonTasks[taskName] || null;
}

function getTroubleshootingInfo(category) {
    return PLATFORM_KNOWLEDGE.troubleshooting[category] || null;
}

function getFeatureInfo(featureName) {
    return PLATFORM_KNOWLEDGE.features[featureName] || null;
}

function searchKnowledge(query) {
    const results = [];
    const searchTerm = query.toLowerCase();
    
    // Search in common tasks
    Object.entries(PLATFORM_KNOWLEDGE.commonTasks).forEach(([key, task]) => {
        if (key.toLowerCase().includes(searchTerm) || 
            task.steps.some(step => step.toLowerCase().includes(searchTerm))) {
            results.push({ type: 'task', key, data: task });
        }
    });
    
    // Search in troubleshooting
    Object.entries(PLATFORM_KNOWLEDGE.troubleshooting).forEach(([key, info]) => {
        if (key.toLowerCase().includes(searchTerm) || 
            info.some(item => item.toLowerCase().includes(searchTerm))) {
            results.push({ type: 'troubleshooting', key, data: info });
        }
    });
    
    // Search in features
    Object.entries(PLATFORM_KNOWLEDGE.features).forEach(([key, feature]) => {
        if (key.toLowerCase().includes(searchTerm) || 
            feature.description.toLowerCase().includes(searchTerm)) {
            results.push({ type: 'feature', key, data: feature });
        }
    });
    
    return results;
}

module.exports = {
    PLATFORM_KNOWLEDGE,
    getKnowledgeForRole,
    getCommonTask,
    getTroubleshootingInfo,
    getFeatureInfo,
    searchKnowledge
};
