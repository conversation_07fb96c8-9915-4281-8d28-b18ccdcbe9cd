'use strict';

const Router = require('koa-router');
const appRouter = Router();

// Keep in this order as the redirects will not work correctly otherwise
appRouter.use(require('./metrc').routes());
appRouter.use(require('./retailer').routes());
appRouter.use(require('./vendor').routes());
appRouter.use(require('./admin').routes());

appRouter.use(require('./ai-assistant').routes());
appRouter.use(require('./analytics').routes());
appRouter.use(require('./system').routes());
appRouter.use(require('./site').routes());



module.exports = appRouter;
