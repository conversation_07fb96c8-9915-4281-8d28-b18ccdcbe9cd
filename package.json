{"name": "server", "version": "1.0.0", "main": "app.js", "license": "MIT", "scripts": {"start": "gulp watch", "start:queue": "nodemon -x node --inspect-port=9228 ./backend/src/cli/queue.js", "start:maildev": "npx gulp maildev", "maildev:docker:start": "./scripts/maildev-docker.sh start", "maildev:docker:stop": "./scripts/maildev-docker.sh stop", "maildev:docker:restart": "./scripts/maildev-docker.sh restart", "compile": "parallel-webpack --config ./webpack/production.config.js --bail ", "analyze": "node webpack/web-stat.js", "watch": "webpack-dev-server --config ./webpack/development.config.js -d --progress --stats errors-only", "migrate": "node ./backend/src/migrations/migrate.js up", "migrate:clean": "node ./backend/src/migrations/migrate.js clean", "eslint": "eslint --ext .js,.vue backend/src frontend/src tests --fix", "test:e2e": "codeceptjs run --steps", "test:e2e:vendor": "codeceptjs run tests/steps/vendor/navigationSteps.js", "test:e2e:debug": "node --inspect ./node_modules/.bin/codeceptjs run --steps", "test:e2e:show": "CODECEPT_SHOW=1 node --inspect=9234 ./node_modules/.bin/codeceptjs run --steps", "test:report": "codeceptjs run-multiple parallel --reporter tap", "test:migrate": "node ./backend/src/migrations/migrate.js tests"}, "devDependencies": {"@babel/core": "^7.8.4", "@babel/plugin-syntax-dynamic-import": "^7.8.3", "@babel/plugin-transform-runtime": "^7.8.3", "@babel/polyfill": "^7.8.3", "@babel/preset-env": "^7.8.4", "@babel/preset-stage-2": "^7.8.3", "@babel/register": "^7.8.3", "@babel/runtime": "^7.8.4", "@babel/runtime-corejs2": "^7.8.4", "babel-eslint": "^10.0.3", "babel-loader": "^8.0.6", "bootstrap-sass": "3.4.1", "chai": "^4.2.0", "clean-webpack-plugin": "^3.0.0", "codeceptjs": "^2.5.0", "css-loader": "^3.4.2", "eslint": "^6.8.0", "eslint-loader": "^3.0.3", "eslint-plugin-vue": "^6.2.1", "extract-text-webpack-plugin": "^3.0.2", "faker": "^4.1.0", "fast-sass-loader": "^1.5.0", "file-loader": "^4.2.0", "gulp": "^4.0.2", "gulp-imagemin": "^7.1.0", "gulp-livereload": "^4.0.2", "gulp-nodemon": "^2.4.2", "gulp-sass": "^4.0.2", "gulp-sourcemaps": "^2.6.5", "img-loader": "^3.0.1", "mocha": "^7.0.1", "mochawesome": "^4.1.0", "nodemon": "^2.0.2", "parallel-webpack": "^2.4.0", "puppeteer": "^2.1.1", "request-json": "^0.6.4", "sass": "^1.69.5", "sass-flex-mixin": "^1.0.3", "sass-loader": "^10.4.1", "svg-inline-loader": "^0.8.2", "vue-loader": "^15.9.0", "webpack": "^4.41.6", "webpack-bundle-analyzer": "^3.6.0", "webpack-cli": "^3.3.11", "webpack-dev-server": "^3.10.3", "webpack-manifest-plugin": "^2.2.0", "webpack-merge": "^4.2.2"}, "dependencies": {"@hubspot/api-client": "^13.2.0", "@koa/multer": "^3.0.2", "@sentry/browser": "^5.12.4", "@sentry/integrations": "^5.12.4", "@sentry/node": "^6.13.2", "@sentry/tracing": "^6.13.2", "@vee-validate/rules": "^4.10.0", "agenda": "^3.0.0", "amplitude-js": "^5.9.0", "axios": "^0.19.2", "bcryptjs": "^2.4.3", "bluebird": "^3.7.2", "bootstrap": "3", "card-validator": "^6.2.0", "chart.js": "^2.9.3", "child_process": "^1.0.2", "chokidar": "^3.5.3", "creditcardutils": "^1.0.0", "crypto": "^1.0.1", "csv-parser": "^3.0.0", "csv-stringify": "^5.3.6", "dot-object": "^2.1.3", "escape-string-regexp": "^2.0.0", "exceljs": "^3.8.0", "file-icon-vectors": "^1.0.0", "file-saver": "^2.0.5", "flat-color-icons": "^1.1.0", "gsap": "^3.2.0", "html-entities": "^1.2.1", "htmlescape": "^1.1.1", "jquery": "^3.4.1", "js-cookie": "^2.2.1", "jsonwebtoken": "^8.5.1", "koa": "^2.11.0", "koa-basic-auth": "4.0.0", "koa-bodyparser": "4.2.1", "koa-connect": "2.0.1", "koa-convert": "^1.2.0", "koa-cors": "^0.0.16", "koa-express": "^1.1.0", "koa-livereload": "^0.2.0", "koa-logger": "3.2.1", "koa-proxy": "^0.9.0", "koa-router": "^8.0.8", "koa-session": "^5.13.1", "koa-static": "5.0.0", "koa2-proxies": "^1.0.2", "koa2-request": "^1.0.4", "lodash": "^4.17.15", "lodash.clonedeep": "^4.5.0", "luxon": "^1.22.0", "maildev": "^1.1.0", "mini-css-extract-plugin": "^0.9.0", "minimist": "^1.2.0", "moment": "^2.29.1", "mongoose": "^5.9.2", "mongoose-pagination": "^1.0.0", "multer": "^1.4.5-lts.1", "nodemailer": "^6.4.3", "nunjucks": "^3.2.0", "openai": "^4.104.0", "password-generator": "^2.2.3", "pretty-file-icons": "^2.2.1", "recursive-readdir-sync": "^1.0.6", "request-promise": "^4.2.5", "scrollmagic": "^2.0.7", "shortid": "^2.2.15", "slack": "^11.0.2", "slick-carousel": "^1.8.1", "sparkpost": "^2.1.4", "twilio": "^3.39.5", "uploadcare-widget": "^3.8.3", "uuid": "^3.4.0", "validator": "^12.2.0", "vanilla-text-mask": "^5.1.1", "vee-validate": "^2.2.15", "vue": "^2.6.11", "vue-carousel": "^0.18.0", "vue-cleave-component": "^2.1.3", "vue-clipboard2": "^0.3.3", "vue-color": "^2.7.0", "vue-infinite-loading": "^2.4.4", "vue-multiselect": "^2.1.7", "vue-pdf": "^4.3.0", "vue-popperjs": "^2.3.0", "vue-router": "^3.1.5", "vue-slick": "^1.1.15", "vue-slider-component": "^2.8.16", "vue-svg-inline-loader": "^1.4.5", "vue-template-compiler": "^2.6.11", "vue2-datepicker": "^3.3.1", "vuex": "^3.1.2", "vuex-router-sync": "^5.0.0", "xlsx": "^0.18.5"}}