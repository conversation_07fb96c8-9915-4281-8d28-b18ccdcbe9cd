<template>
    <div class="page">
        <header-component></header-component>

        <div class="d-table-row">
            <div class="d-table-cell text-top">
                <verify-email class="mt-6"></verify-email>
            </div>
        </div>

        <div class="d-table-row" v-if="isDomestic">
            <div class="d-table-cell text-top">
                <metrc-input-key class="mt-6"></metrc-input-key>
            </div>
        </div>

        <div class="content-row">
            <div class="d-table-cell text-top">
                <div class="container my-6">
                    <router-view :key="$route.path"></router-view>
                </div>
            </div>
        </div>

        <footer-component role="vendor"></footer-component>
        <modal-component ref="modal"></modal-component>
        <ai-assistant></ai-assistant>
    </div>
</template>


<script>
import FooterComponent from '@/common/components/Footer';
import VerifyEmail from '@/common/components/VerifyEmail';
import MetrcInputKey from '@/common/components/MetrcInputKey';
import { FlashService } from '@/common/FlashService';
import HttpPlugin from '@/common/plugins/httpPlugin';
import TermsAndConditionsModal from '@/vendor/components/TermsAndConditionsModal';
import Vue from 'vue';
import { sync } from 'vuex-router-sync';
import { mapGetters } from 'vuex';
import createRouter from '../router';
import store, {
    couponService,
    metrcProductService,
    orderService,
    productService,
    ticketService,
    userService,
    vendorService,
    loggerService
} from '../store';
import HeaderComponent from './Header';
import AIAssistant from '@/common/components/AIAssistant';

Vue.use(HttpPlugin, { store });

const router = createRouter(store);
router.afterEach((to) => {
    loggerService.pageView({ page: to.path });
});

sync(store, router, { moduleName: 'RouteModule' });
const flashService = new FlashService();

export default {
    name: 'VendorModule',
    provide: {
        $couponService: couponService,
        $userService: userService,
        $ticketService: ticketService,
        $productService: productService,
        $metrcProductService: metrcProductService,
        $orderService: orderService,
        $vendorService: vendorService,
        $logger: loggerService
    },
    components: {
        VerifyEmail,
        MetrcInputKey,
        HeaderComponent,
        FooterComponent,
        AIAssistant
    },
    router,
    store,
    computed: {
        ...mapGetters(['state']),
        isDomestic() {
            return this.state.isDomestic;
        }
    },
    mounted() {
        this.$modal.setTarget(this.$refs.modal);
        flashService.setTarget(this.$refs.flash);

        if (!store.state.shadowLogin && store.state.vendor.needAcceptNewTerms) {
            this.$modal.open(TermsAndConditionsModal, {
                size: 'modal-800',
                disableClose: true
            });
        }
    }
};
</script>
