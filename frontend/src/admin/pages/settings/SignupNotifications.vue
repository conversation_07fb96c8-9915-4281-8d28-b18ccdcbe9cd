<template>
    <div class="signup-notifications">
        <div class="page-header">
            <h1>Signup Notifications</h1>
            <p class="text-muted">Manage email notifications for new user signups</p>
        </div>

        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">Email Recipients</h5>
            </div>
            <div class="card-body">
                <form @submit.prevent="saveSettings">
                    <div class="form-group">
                        <label>Admin Email Addresses</label>
                        <p class="text-muted small">Enter email addresses that should receive notifications when new users sign up. One email per line.</p>
                        <textarea 
                            v-model="emailsText" 
                            class="form-control" 
                            rows="6" 
                            placeholder="<EMAIL>&#10;<EMAIL>&#10;<EMAIL>"
                        ></textarea>
                        <div v-if="errors.emails" class="text-danger mt-1">
                            <small v-for="error in errors.emails" :key="error">{{ error }}</small>
                        </div>
                    </div>

                    <div class="form-group">
                        <div class="form-check">
                            <input 
                                v-model="enabled" 
                                class="form-check-input" 
                                type="checkbox" 
                                id="enableNotifications"
                            >
                            <label class="form-check-label" for="enableNotifications">
                                Enable signup notifications
                            </label>
                        </div>
                        <small class="text-muted">When disabled, no email notifications will be sent for new signups</small>
                    </div>

                    <div class="d-flex justify-content-between">
                        <button type="submit" class="btn btn-primary" :disabled="saving">
                            <i v-if="saving" class="fa fa-spinner fa-spin mr-1"></i>
                            {{ saving ? 'Saving...' : 'Save Settings' }}
                        </button>
                        
                        <button type="button" class="btn btn-outline-secondary" @click="testNotification" :disabled="testing">
                            <i v-if="testing" class="fa fa-spinner fa-spin mr-1"></i>
                            {{ testing ? 'Sending...' : 'Send Test Email' }}
                        </button>
                    </div>
                </form>
            </div>
        </div>

        <div class="card mt-4">
            <div class="card-header">
                <h5 class="mb-0">Email Template Preview</h5>
            </div>
            <div class="card-body">
                <p class="text-muted">This is what admins will receive when a new user signs up:</p>
                <div class="border p-3 bg-light">
                    <strong>Subject:</strong> New User Signup - [User Name]<br>
                    <strong>Content:</strong> Notification about new user registration with user details and account type
                </div>
            </div>
        </div>
    </div>
</template>

<script>
import { mapActions } from 'vuex';

export default {
    name: 'SignupNotifications',
    data() {
        return {
            emailsText: '',
            enabled: true,
            saving: false,
            testing: false,
            errors: {}
        };
    },
    async mounted() {
        await this.loadSettings();
    },
    methods: {
        ...mapActions(['showNotification']),
        
        async loadSettings() {
            try {
                const response = await this.$http.get('/admin/settings/signup-notifications');
                const settings = response.data;
                
                this.emailsText = (settings.emails || []).join('\n');
                this.enabled = settings.enabled !== false;
            } catch (error) {
                console.error('Failed to load settings:', error);
                this.showNotification({
                    type: 'error',
                    message: 'Failed to load signup notification settings'
                });
            }
        },
        
        async saveSettings() {
            this.saving = true;
            this.errors = {};
            
            try {
                const emails = this.emailsText
                    .split('\n')
                    .map(email => email.trim())
                    .filter(email => email.length > 0);
                
                await this.$http.put('/admin/settings/signup-notifications', {
                    emails,
                    enabled: this.enabled
                });
                
                this.showNotification({
                    type: 'success',
                    message: 'Signup notification settings saved successfully'
                });
            } catch (error) {
                console.error('Failed to save settings:', error);
                
                if (error.response && error.response.data && error.response.data.errors) {
                    this.errors = error.response.data.errors;
                } else {
                    this.showNotification({
                        type: 'error',
                        message: 'Failed to save settings'
                    });
                }
            } finally {
                this.saving = false;
            }
        },
        
        async testNotification() {
            this.testing = true;
            
            try {
                await this.$http.post('/admin/settings/signup-notifications/test');
                
                this.showNotification({
                    type: 'success',
                    message: 'Test email sent successfully'
                });
            } catch (error) {
                console.error('Failed to send test email:', error);
                this.showNotification({
                    type: 'error',
                    message: 'Failed to send test email'
                });
            } finally {
                this.testing = false;
            }
        }
    }
};
</script>

<style scoped>
.signup-notifications {
    max-width: 800px;
}

.page-header {
    margin-bottom: 2rem;
}

.card {
    box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
    border: 1px solid rgba(0, 0, 0, 0.125);
}

.card-header {
    background-color: #f8f9fa;
    border-bottom: 1px solid rgba(0, 0, 0, 0.125);
}

textarea.form-control {
    font-family: monospace;
    font-size: 0.9rem;
}
</style>
