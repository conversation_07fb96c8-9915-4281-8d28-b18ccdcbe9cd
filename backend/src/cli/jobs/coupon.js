
'use strict';
const queue = require('../../services/queue');
const mongoose = require('mongoose');
const constants = require('../../models/constants');
const sentry = require('../../services/sentry');

const logger = sentry.logger('job.coupons');

module.exports = function (agenda) {
    // a job to check if coupon is expired
    agenda.define(queue.constants.COUPON.CHECK, (job, done) => {
        checkCouponsExpiredDate()
            .then(() => {
                done();
            })
            .catch(err => {
                console.error(err);
                logger(err, {
                    tags: {
                        queue: queue.constants.COUPON.CHECK
                    }
                });
                done(err);
            });
    });
    agenda.every(
        '1 day',
        queue.constants.COUPON.CHECK,
        {},
        {
            skipImmediate: true
        }
    );
};

async function checkCouponsExpiredDate() {
    const { Coupon, SystemLog } = mongoose.models;

    const now = new Date();
    const expiredCoupons = await Coupon.find({ 'expiredAt': { $lt: now, $ne: null } }, { _id: 1 });
    const expiredCouponIDs = expiredCoupons.map(coupon => coupon._id);

    const couponList = await Coupon.find({
        status: [constants.COUPON.STATUS.ACTIVE, constants.COUPON.STATUS.INACTIVE],
        $or: [
            { expiredAt: { $lt: now, $ne: null } },
            { _id: { $in: expiredCouponIDs } }
        ]
    });

    for (const coupon of couponList) {
        coupon.set({
            status: constants.COUPON.STATUS.EXPIRED
        });
        await coupon.save();        
        await SystemLog.scriptCouponExpired(coupon);
    }
}