# CanIDeal AI Assistant - Testing & Deployment Guide

## 🚀 Quick Start

### 1. Environment Setup
```bash
# Set OpenAI API Key
export OPENAI_API_KEY="your-openai-api-key-here"

# Or add to your .env file
echo "OPENAI_API_KEY=your-openai-api-key-here" >> .env
```

### 2. Start the Application
```bash
# Terminal 1: Start the main application
npm run start

# Terminal 2: Start the queue processor
npm run start:queue

# Terminal 3: Start the file watcher
npm run watch

# Terminal 4: Start Docker (if needed for database)
docker-compose up -d
```

### 3. Access the AI Assistant
- **Vendor Dashboard**: Login as vendor → Look for floating AI assistant button (bottom right)
- **Retailer Dashboard**: Login as retailer → Look for floating AI assistant button (bottom right)  
- **Admin Panel**: Login as admin → Look for floating AI assistant button (bottom right)

## 🧪 Testing Scenarios

### Basic Functionality Tests

#### Test 1: AI Assistant Visibility
- [ ] AI assistant button appears on all dashboards (vendor, retailer, admin)
- [ ] But<PERSON> is positioned correctly (bottom right, floating)
- [ ] But<PERSON> has proper styling and hover effects
- [ ] Clicking button opens chat interface

#### Test 2: Chat Interface
- [ ] Chat window opens with welcome message
- [ ] Welcome message includes role-specific suggestions
- [ ] Message input field is functional
- [ ] Send button works correctly
- [ ] Messages display properly with timestamps

#### Test 3: Basic AI Responses
- [ ] Ask "How do I change my password?" → Should get step-by-step instructions
- [ ] Ask "How do I update my profile?" → Should get navigation guidance
- [ ] Ask "How do I contact support?" → Should explain ticket system
- [ ] Ask general platform questions → Should provide relevant answers

### Role-Specific Testing

#### Vendor Tests
```
Test Messages:
- "How do I add a new product?"
- "How do I manage my inventory?"
- "How do I view my orders?"
- "How do I set up Metrc integration?"
- "How do I import products from a spreadsheet?"

Expected: Role-specific guidance with exact navigation steps
```

#### Retailer Tests
```
Test Messages:
- "How do I search for products?"
- "How do I place an order?"
- "How do I track my orders?"
- "How do I contact a vendor?"
- "How do I update my business information?"

Expected: Retailer-focused responses with marketplace guidance
```

#### Admin Tests
```
Test Messages:
- "How do I review applications?"
- "How do I manage users?"
- "How do I send email campaigns?"
- "How do I view platform analytics?"
- "How do I handle support tickets?"

Expected: Admin-specific instructions with platform management guidance
```

### Advanced Feature Tests

#### Test 4: Conversation Memory
- [ ] Start conversation with "My name is John"
- [ ] In next message ask "What's my name?" → Should remember "John"
- [ ] Ask follow-up questions → Should maintain context
- [ ] Refresh page → Should maintain session context

#### Test 5: Action Execution
- [ ] Ask "Help me change my password" → Should offer to execute action
- [ ] Try updating profile information → Should validate permissions
- [ ] Create support ticket through AI → Should work end-to-end
- [ ] Test permission boundaries → Should respect user roles

#### Test 6: Error Handling
- [ ] Test with invalid OpenAI API key → Should show graceful error
- [ ] Test with network issues → Should provide fallback responses
- [ ] Test rate limiting → Should handle gracefully
- [ ] Test malformed requests → Should validate properly

## 🔧 API Endpoint Testing

### Manual API Tests
```bash
# Test chat endpoint
curl -X POST http://localhost:3000/ai-assistant/chat \
  -H "Content-Type: application/json" \
  -d '{"message": "How do I change my password?", "context": {"userRole": "vendor"}}'

# Test suggestions endpoint
curl -X GET http://localhost:3000/ai-assistant/suggestions

# Test available actions
curl -X GET http://localhost:3000/ai-assistant/available-actions

# Test conversation stats (admin only)
curl -X GET http://localhost:3000/ai-assistant/conversation-stats
```

## 🐛 Common Issues & Solutions

### Issue 1: AI Assistant Not Appearing
**Symptoms**: Button doesn't show up on dashboard
**Solutions**:
- Check browser console for JavaScript errors
- Verify component is imported in module files
- Check if user is properly authenticated
- Ensure CSS is loading correctly

### Issue 2: OpenAI API Errors
**Symptoms**: "AI service not available" messages
**Solutions**:
- Verify OPENAI_API_KEY is set correctly
- Check API key has sufficient credits
- Verify network connectivity
- Check server logs for detailed error messages

### Issue 3: Conversation Context Not Working
**Symptoms**: AI doesn't remember previous messages
**Solutions**:
- Check if user is properly authenticated
- Verify conversation manager is initialized
- Check browser sessionStorage for session ID
- Review server logs for context updates

### Issue 4: Permission Errors
**Symptoms**: "Insufficient permissions" for actions
**Solutions**:
- Verify user role is correctly identified
- Check permission mapping in action executor
- Ensure user has required permissions
- Review user context building logic

## 📊 Performance Monitoring

### Key Metrics to Monitor
- **Response Time**: AI responses should be under 3 seconds
- **Error Rate**: Should be less than 5% of requests
- **Memory Usage**: Conversation store should not grow unbounded
- **API Usage**: Monitor OpenAI token consumption

### Monitoring Commands
```bash
# Check conversation statistics
curl -X GET http://localhost:3000/ai-assistant/conversation-stats

# Monitor server logs
tail -f logs/application.log | grep "AI Assistant"

# Check memory usage
ps aux | grep node
```

## 🔒 Security Checklist

- [ ] OpenAI API key is stored securely (environment variable)
- [ ] User permissions are properly validated
- [ ] Sensitive data is not logged
- [ ] Rate limiting is working correctly
- [ ] Input validation prevents injection attacks
- [ ] Conversation data is properly isolated by user

## 📱 Mobile Testing

### Mobile Responsiveness Tests
- [ ] Test on iPhone (Safari)
- [ ] Test on Android (Chrome)
- [ ] Test on tablet devices
- [ ] Verify chat interface adapts to screen size
- [ ] Check touch interactions work properly
- [ ] Ensure keyboard doesn't obscure input

## 🚀 Production Deployment

### Pre-Deployment Checklist
- [ ] All tests pass
- [ ] OpenAI API key configured in production
- [ ] Rate limiting configured appropriately
- [ ] Logging is set up for monitoring
- [ ] Error handling is comprehensive
- [ ] Performance is acceptable under load

### Deployment Steps
1. Set production environment variables
2. Deploy backend changes
3. Deploy frontend changes
4. Verify AI assistant appears on all dashboards
5. Test basic functionality
6. Monitor for errors and performance issues

### Post-Deployment Monitoring
- Monitor OpenAI API usage and costs
- Track user engagement with AI assistant
- Monitor error rates and response times
- Collect user feedback for improvements

## 📈 Success Metrics

### User Engagement
- Number of AI assistant interactions per day
- Average conversation length
- User retention after using AI assistant
- Reduction in support ticket volume

### Technical Performance
- Average response time < 3 seconds
- Error rate < 5%
- 99.9% uptime
- Efficient token usage

### Business Impact
- Reduced support burden
- Improved user onboarding
- Higher user satisfaction scores
- Increased platform engagement

## 🔄 Continuous Improvement

### Regular Tasks
- Review conversation logs for improvement opportunities
- Update knowledge base with new features
- Optimize prompts based on user interactions
- Monitor and adjust rate limits
- Clean up old conversation data

### Feature Enhancements
- Add more action handlers
- Improve conversation context
- Implement user feedback system
- Add analytics dashboard
- Integrate with more platform features
