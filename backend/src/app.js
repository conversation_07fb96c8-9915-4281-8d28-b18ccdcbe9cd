'use strict';

const config = require('./services/config');
const koa = require('koa');
const app = new koa();
const session = require('koa-session');
const logger = require('koa-logger');
const cors = require('koa-cors');
const livereload = require('koa-livereload');
const bodyparser = require('koa-bodyparser');
const http = require('http');
const constants = require('./models/constants');
const mw = require('./services/middleware');
const view = require('./services/view');
const db = require('./services/db');
const childProcess = require('child_process');
const sentry = require('./services/sentry');
const queue = require('./services/queue');
const appRouter = require('./controllers');

const Sentry = require('@sentry/node');
const Tracing = require('@sentry/tracing');

Sentry.init({
    dsn: 'https://<EMAIL>/5983220',

    // Set tracesSampleRate to 1.0 to capture 100%
    // of transactions for performance monitoring.
    // We recommend adjusting this value in production
    tracesSampleRate: 1.0
});

const transaction = Sentry.startTransaction({
    op: 'test',
    name: 'My First Test Transaction'
});

App()
    .catch(Err);

async function App() {

    app.keys = config.app.keys; // i.e. ['test']
    db.connect();


    view(app);
    queue.init();

    // Initialize OpenAI service
    const openaiService = require('./services/openai');
    openaiService.init();

    // Initialize HubSpot services
    const hubspotService = require('./services/hubspot');
    const hubspotSync = require('./services/hubspot-sync');
    const campaignTriggers = require('./services/campaign-triggers');

    hubspotService.init();
    hubspotSync.initHubSpotSync();
    campaignTriggers.initCampaignTriggers();

    // Initialize analytics services
    const userAnalytics = require('./services/user-analytics');
    const enhancedTracking = require('./services/enhanced-tracking');
    console.log('User analytics and enhanced tracking initialized');

    app
        .use(session(config.session, app))
        .use(mw.timerHeader);

    // if (config.app.env !== constants.ENV.PRODUCTION) {
    //     app.use(logger());
    //     app.use(mw.maildev());
    // }
    // if (config.app.env === constants.ENV.DEVELOPMENT) {
    //     app.use(livereload());
    //     app.use(mw.requestDelay);
    // }

    app
        .use(mw.staticHandler)
        .use(bodyparser())
        .use(mw.isDataRequest)
        .use(mw.userAuth)
        .use(mw.pagination)
        .use(mw.loadStates) // Loads the states to show on the frontend
        .use(mw.flash)
        .use(mw.errorHandler)
        .use(appRouter.routes());

    let server = http.createServer(app.callback());
    let port = process.env.PORT || 3000;
    server.listen(port);
    server.once('listening', () => {
        console.log(`CanIDeal listening on port ${port}`);
    });

    process.on('SIGTERM', () => {
        console.log('Gracefully shutting down');
        server.close(() => {
            process.exit(0);
        });
    });
}

function Err(err) {
    Sentry.captureException(err);
    console.error(err.stack);
    process.exit(1);
}




