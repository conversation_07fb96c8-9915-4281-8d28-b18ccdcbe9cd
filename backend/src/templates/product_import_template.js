'use strict';

const xlsx = require('xlsx');
const path = require('path');
const fs = require('fs');

/**
 * Generate a product import template file
 * @returns {Buffer} Excel file buffer
 */
function generateTemplate() {
  const workbook = xlsx.utils.book_new();

  // Create headers for the Products sheet
  const productHeaders = [
    'SKU*',
    'Name*',
    'Description',
    'Category*',
    'Type*',
    'Status',
    'Strain',
    'THC Content (%)',
    'CBD Content (%)',
    'Return Policy',
    'Main Image URL',
    'Additional Image URLs (comma separated)'
  ];

  // Create headers for the Variants sheet
  const variantHeaders = [
    'Product SKU*',
    'Variant SKU*',
    'Price*',
    'Quantity*',
    'Weight',
    'Size',
    'Custom1',
    'Custom2',
    'Delivery Price'
  ];

  // Create headers for the Batches sheet
  const batchHeaders = [
    'Product SKU*',
    'Batch ID*',
    'Batch Name*',
    'COA Image URL',
    'Quantity'
  ];

  // Add example data for Products
  const productData = [
    productHeaders,
    [
      'PROD001',
      'Example Hemp CBD Oil',
      'High-quality hemp-derived CBD oil for relaxation and wellness.',
      'Hemp derived CBD',
      'hemp_derived_cbd',
      'Inactive',
      '',
      '',
      '5',
      '30-day money back guarantee',
      'https://example.com/images/cbd-oil.jpg',
      'https://example.com/images/cbd-oil-2.jpg,https://example.com/images/cbd-oil-3.jpg'
    ],
    [
      'PROD002',
      'Organic Hemp Gummies',
      'Delicious hemp gummies made with organic ingredients.',
      'Hemp derived CBD',
      'hemp_derived_cbd',
      'Inactive',
      '',
      '',
      '10',
      'No returns on edibles',
      'https://example.com/images/gummies.jpg',
      ''
    ],
    [
      'PROD003',
      'CBD Vape Pen',
      'Convenient vape pen with premium CBD extract.',
      'Ancillary',
      'ancillary',
      'Inactive',
      '',
      '',
      '15',
      '14-day return policy',
      'https://example.com/images/vape-pen.jpg',
      'https://example.com/images/vape-pen-2.jpg'
    ]
  ];

  // Add example data for Variants
  const variantData = [
    variantHeaders,
    ['PROD001', 'PROD001-S', '29.99', '100', '30ml', 'Small', 'Mint', 'Regular Strength', '5.99'],
    ['PROD001', 'PROD001-M', '49.99', '75', '60ml', 'Medium', 'Mint', 'Extra Strength', '5.99'],
    ['PROD001', 'PROD001-L', '79.99', '50', '120ml', 'Large', 'Mint', 'Maximum Strength', '0'],
    ['PROD002', 'PROD002-20', '19.99', '200', '100g', '20 count', 'Mixed Berry', 'Regular Strength', '4.99'],
    ['PROD002', 'PROD002-40', '34.99', '150', '200g', '40 count', 'Mixed Berry', 'Regular Strength', '4.99'],
    ['PROD003', 'PROD003-B', '39.99', '75', '5g', 'Blue', 'Disposable', '250mg', '3.99'],
    ['PROD003', 'PROD003-G', '39.99', '75', '5g', 'Green', 'Disposable', '250mg', '3.99']
  ];

  // Add example data for Batches
  const batchData = [
    batchHeaders,
    ['PROD001', 'BATCH001', 'January Production', 'https://example.com/coa/batch001.pdf', '225'],
    ['PROD002', 'BATCH002', 'February Production', 'https://example.com/coa/batch002.pdf', '350'],
    ['PROD003', 'BATCH003', 'March Production', 'https://example.com/coa/batch003.pdf', '150']
  ];

  // Create worksheets
  const productsWorksheet = xlsx.utils.aoa_to_sheet(productData);
  const variantsWorksheet = xlsx.utils.aoa_to_sheet(variantData);
  const batchesWorksheet = xlsx.utils.aoa_to_sheet(batchData);

  // Add worksheets to workbook
  xlsx.utils.book_append_sheet(workbook, productsWorksheet, 'Products');
  xlsx.utils.book_append_sheet(workbook, variantsWorksheet, 'Variants');
  xlsx.utils.book_append_sheet(workbook, batchesWorksheet, 'Batches');

  // Add instructions worksheet
  const instructionsData = [
    ['Product Import Instructions'],
    [''],
    ['This template contains three sheets:'],
    ['1. Products - Basic product information'],
    ['2. Variants - Product variants with pricing and inventory'],
    ['3. Batches - Batch information for products'],
    [''],
    ['Required fields are marked with an asterisk (*)'],
    [''],
    ['Field Descriptions:'],
    [''],
    ['Products Sheet:'],
    ['SKU* - Unique identifier for the product (required)'],
    ['Name* - Product name (required)'],
    ['Description - Detailed product description'],
    ['Category* - Product category (required). Must match one of the system categories'],
    ['Type* - Product type (required). Valid values: hemp_derived_cbd, flower_derived_cbd, thc, ancillary'],
    ['Status - Product status. Valid values: Active, Inactive. Default is Inactive'],
    ['Strain - Cannabis strain name (for cannabis products)'],
    ['THC Content (%) - THC percentage (for cannabis products)'],
    ['CBD Content (%) - CBD percentage'],
    ['Return Policy - Product return policy'],
    ['Main Image URL - URL to the main product image'],
    ['Additional Image URLs - Comma-separated list of URLs for additional product images'],
    [''],
    ['Variants Sheet:'],
    ['Product SKU* - Must match a SKU from the Products sheet (required)'],
    ['Variant SKU* - Unique identifier for this variant (required)'],
    ['Price* - Retail price (required)'],
    ['Quantity* - Available inventory (required)'],
    ['Weight - Product weight with unit (e.g., 30ml, 5g)'],
    ['Size - Product size (e.g., Small, Medium, Large)'],
    ['Custom1 - Custom attribute 1 (e.g., Color, Flavor)'],
    ['Custom2 - Custom attribute 2 (e.g., Strength, Style)'],
    ['Delivery Price - Shipping cost for this variant'],
    [''],
    ['Batches Sheet:'],
    ['Product SKU* - Must match a SKU from the Products sheet (required)'],
    ['Batch ID* - Unique identifier for this batch (required)'],
    ['Batch Name* - Name of the batch (required)'],
    ['COA Image URL - URL to Certificate of Analysis document'],
    ['Quantity - Total quantity in this batch']
  ];

  const instructionsWorksheet = xlsx.utils.aoa_to_sheet(instructionsData);
  xlsx.utils.book_append_sheet(workbook, instructionsWorksheet, 'Instructions');

  // Set column widths for better readability
  const setColumnWidths = (worksheet) => {
    const columnWidths = [
      { wch: 15 }, // A
      { wch: 25 }, // B
      { wch: 40 }, // C
      { wch: 20 }, // D
      { wch: 15 }, // E
      { wch: 15 }, // F
      { wch: 15 }, // G
      { wch: 15 }, // H
      { wch: 15 }, // I
      { wch: 25 }, // J
      { wch: 40 }, // K
      { wch: 60 }  // L
    ];
    worksheet['!cols'] = columnWidths;
  };

  setColumnWidths(productsWorksheet);
  setColumnWidths(variantsWorksheet);
  setColumnWidths(batchesWorksheet);
  setColumnWidths(instructionsWorksheet);

  // Save to buffer
  const buffer = xlsx.write(workbook, { type: 'buffer', bookType: 'xlsx' });

  // Ensure the templates directory exists
  const templateDir = path.join(__dirname);
  if (!fs.existsSync(templateDir)) {
    fs.mkdirSync(templateDir, { recursive: true });
  }

  // Save to file for future use
  const templatePath = path.join(templateDir, 'product_import_template.xlsx');
  fs.writeFileSync(templatePath, buffer);

  return buffer;
}

/**
 * Generate a CSV template file with just the Products sheet
 * @returns {Buffer} CSV file buffer
 */
function generateCSVTemplate() {
  // Create headers for the Products sheet
  const productHeaders = [
    'SKU*',
    'Name*',
    'Description',
    'Category*',
    'Type*',
    'Status',
    'Strain',
    'THC Content (%)',
    'CBD Content (%)',
    'Return Policy',
    'Main Image URL',
    'Additional Image URLs (comma separated)'
  ];

  // Add example data for Products
  const productData = [
    productHeaders,
    [
      'PROD001',
      'Example Hemp CBD Oil',
      'High-quality hemp-derived CBD oil for relaxation and wellness.',
      'Hemp derived CBD',
      'hemp_derived_cbd',
      'Inactive',
      '',
      '',
      '5',
      '30-day money back guarantee',
      'https://example.com/images/cbd-oil.jpg',
      'https://example.com/images/cbd-oil-2.jpg,https://example.com/images/cbd-oil-3.jpg'
    ],
    [
      'PROD002',
      'Organic Hemp Gummies',
      'Delicious hemp gummies made with organic ingredients.',
      'Hemp derived CBD',
      'hemp_derived_cbd',
      'Inactive',
      '',
      '',
      '10',
      'No returns on edibles',
      'https://example.com/images/gummies.jpg',
      ''
    ],
    [
      'PROD003',
      'CBD Vape Pen',
      'Convenient vape pen with premium CBD extract.',
      'Ancillary',
      'ancillary',
      'Inactive',
      '',
      '',
      '15',
      '14-day return policy',
      'https://example.com/images/vape-pen.jpg',
      'https://example.com/images/vape-pen-2.jpg'
    ]
  ];

  // Create a temporary workbook to convert to CSV
  const workbook = xlsx.utils.book_new();
  const worksheet = xlsx.utils.aoa_to_sheet(productData);
  xlsx.utils.book_append_sheet(workbook, worksheet, 'Products');

  // Convert to CSV
  const csvString = xlsx.utils.sheet_to_csv(worksheet);

  // Save to file for future use
  const templateDir = path.join(__dirname);
  if (!fs.existsSync(templateDir)) {
    fs.mkdirSync(templateDir, { recursive: true });
  }

  const templatePath = path.join(templateDir, 'product_import_template.csv');
  fs.writeFileSync(templatePath, csvString);

  return Buffer.from(csvString);
}

/**
 * Generate a template file in the specified format
 * @param {string} format - The format to generate ('xlsx' or 'csv')
 * @returns {Buffer} File buffer in the requested format
 */
function generateTemplateByFormat(format = 'xlsx') {
  if (format.toLowerCase() === 'csv') {
    return generateCSVTemplate();
  } else {
    return generateTemplate();
  }
}

module.exports = {
  generateTemplate,
  generateCSVTemplate,
  generateTemplateByFormat
};
