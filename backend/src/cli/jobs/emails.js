
'use strict';
const queue = require('../../services/queue');
const mongoose = require('mongoose');
const { <PERSON><PERSON><PERSON>og, EmailBlast, User, Vendor, Retailer } = mongoose.models;
// const constants = require('../../models/constants');
const emailService = require('../../services/email');
const { wait } = require('../../services/util');
const sentry = require('../../services/sentry');

const logger = sentry.logger('job.emails');

module.exports = function (agenda) {
    agenda.define(queue.constants.EMAILS.SPARKPOST_WEBHOOK, (job, done) => {
        const payload = job.attrs.data;
        saveSparkpostEvents(payload)
            .then(() => {
                done();
            })
            .catch(err => {
                console.error(err);
                logger(err, {
                    tags: {
                        queue: queue.constants.EMAILS.SPARKPOST_WEBHOOK
                    },
                    extra: {
                        payload
                    }
                });
                done();
            });
    });
    agenda.define(queue.constants.EMAILS.SEND, (job, done) => {
        const payload = job.attrs.data;
        sendEmail(payload)
            .then(() => {
                done();
            })
            .catch(err => {
                console.error('there was an error sendiing the email', err);
                logger(err, {
                    tags: {
                        queue: queue.constants.EMAILS.SEND
                    },
                    extra: {
                        payload
                    }
                });
                done(err);
            });
    });
    agenda.define(queue.constants.EMAIL_BLAST.START, (job, done) => {
        const payload = job.attrs.data;
        sendEmailBlast(payload, job)
            .then(() => {
                done();
            })
            .catch(err => {
                console.error(err);
                logger(err, {
                    tags: {
                        queue: queue.constants.EMAILS.SEND
                    },
                    extra: {
                        payload
                    }
                });
                done(err);
            });
    });

};

const badEvents = ['bounce', 'spam', 'spam_complaint', 'out_of_band', 'policy_rejection', 'list_unsubscribe', 'link_unsubscribe'];
const goodEvents = ['delivery', 'initial_open', 'open', 'click'];

function updateEmailVerified(events, user) {
    if (events.some(event => badEvents.includes(event))) {
        return User.updateOne(user, { $set: { emailVerified: false } }).exec();
    }
    else if (events.some(event => goodEvents.includes(event))) {
        return User.updateOne(user, { $set: { emailVerified: true } }).exec();
    }
    return Promise.resolve();
}

async function saveSparkpostEvents({ emailLogMap, emailBlastMap }) {

    for (const messageId of Object.keys(emailLogMap)) {
        try {
            await EmailLog.updateOne({ _id: messageId }, {
                $push: {
                    events: {
                        $each: emailLogMap[messageId],
                        $sort: { timestamp: 1 }
                    }
                }
            });
            const events = emailLogMap[messageId].map(event => event.type);
            const emailLog = await EmailLog.findById(messageId, '_user');
            await updateEmailVerified(events, { _id: emailLog._user });
        }
        catch (err) {
            logger(err);
        }
    }

    for (const emailBlastKey of Object.keys(emailBlastMap)) {
        try {
            const emailBlast = await EmailBlast.findOne();
            if (!emailBlast) {
                continue;
            }
            const userMap = emailBlastMap[emailBlastKey];
            const update = {};
            Object.entries(userMap).forEach(([userKey, events]) => {
                Object.entries(events).forEach(([event, date]) => {
                    update[`users.${userKey}.events.${event}`] = date;
                });

                updateEmailVerified(Object.keys(userMap[userKey]), { key: userKey });
            });
            await EmailBlast.updateOne({ key: emailBlastKey }, { $set: update });
        }
        catch (err) {
            logger(err);
        }
    }
}

async function sendEmail({ emailLogId }) {
    const emailLog = await EmailLog.findById(emailLogId);
    if (!emailLog) {
        throw new Error('EmailLog not found');
    }
    return emailService.send(emailLog);
}

async function sendEmailBlast({ emailBlastId }, job) {
    const emailBlast = await EmailBlast.findById(emailBlastId);
    if (!emailBlast) {
        throw new Error('EmailBlast not found');
    }

    await emailBlast.populate('_audience _emailTemplate').execPopulate();
    await emailBlast.processEmails();
    const users = Object.values(emailBlast.users)
        .filter(user => user.emailVerified)
        .filter(user => !user.events.injection);
    for (let i = 0; i < users.length; i++) {
        const user = users[i];
        await wait(10);
        await emailService.sendEmailBlast(emailBlast, user);
        await job.touch();
    }
    emailBlast.completedAt = new Date();
    await emailBlast.save();
}
