'use strict';

const mongoose = require('mongoose');
const { HttpError } = require('../../services/error');
const Router = require('koa-router');
const multer = require('@koa/multer');
const xlsx = require('xlsx');
const csv = require('csv-parser');
const fs = require('fs');
const path = require('path');
const { Readable } = require('stream');
const ObjectId = mongoose.Types.ObjectId;

// Configure multer for file uploads
const upload = multer({
  storage: multer.diskStorage({
    destination: function (req, file, cb) {
      const uploadDir = path.join(__dirname, '../../uploads');
      if (!fs.existsSync(uploadDir)) {
        fs.mkdirSync(uploadDir, { recursive: true });
      }
      cb(null, uploadDir);
    },
    filename: function (req, file, cb) {
      const uniqueSuffix = Date.now() + '-' + Math.round(Math.random() * 1E9);
      cb(null, file.fieldname + '-' + uniqueSuffix + path.extname(file.originalname));
    }
  }),
  fileFilter: function (req, file, cb) {
    // Accept only csv and excel files
    if (
      file.mimetype === 'text/csv' ||
      file.mimetype === 'application/vnd.ms-excel' ||
      file.mimetype === 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
    ) {
      cb(null, true);
    } else {
      cb(new HttpError(400, 'Only CSV and Excel files are allowed'));
    }
  },
  limits: {
    fileSize: 5 * 1024 * 1024 // 5MB limit
  }
});

module.exports = Router()
  .prefix('/import')
  .post('/validate', upload.single('file'), validateImport)
  .post('/', upload.single('file'), importProducts)
  .get('/template', getTemplate);

// Controller: Get a template file for product imports
async function getTemplate(ctx) {
  const templateGenerator = require('../../templates/product_import_template');
  const format = ctx.query.format || 'xlsx';

  let buffer;
  let filename;
  let contentType;

  if (format.toLowerCase() === 'csv') {
    buffer = templateGenerator.generateCSVTemplate();
    filename = 'product_import_template.csv';
    contentType = 'text/csv';
  } else {
    buffer = templateGenerator.generateTemplate();
    filename = 'product_import_template.xlsx';
    contentType = 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet';
  }

  ctx.set('Content-Type', contentType);
  ctx.attachment(filename);
  ctx.body = buffer;
}

// Controller: Validate a product import file
async function validateImport(ctx) {
  const { file } = ctx.request;

  if (!file) {
    throw new HttpError(400, 'No file uploaded');
  }

  try {
    // Parse the file into sheets
    console.log(`Parsing file: ${file.path}, size: ${file.size}, type: ${file.mimetype}`);
    const fileData = await parseFileWithSheets(file.path);

    // Log the parsed data structure
    console.log('Parsed file data structure:', {
      hasProducts: fileData.products && fileData.products.length > 0,
      productsCount: fileData.products ? fileData.products.length : 0,
      hasVariants: fileData.variants && fileData.variants.length > 0,
      variantsCount: fileData.variants ? fileData.variants.length : 0,
      hasBatches: fileData.batches && fileData.batches.length > 0,
      batchesCount: fileData.batches ? fileData.batches.length : 0
    });

    // Validate that the file matches the expected template
    const isTemplateValid = validateTemplate(fileData);
    console.log('Template validation result:', isTemplateValid);

    // If template validation fails, return early with an error
    if (!isTemplateValid) {
      // Clean up the uploaded file
      fs.unlinkSync(file.path);

      // Get the actual headers from the file if available
      let errorMessage = 'The file does not match the expected template format. Please download and use the template file.';

      // If it's an empty template (just headers, no data), provide a specific message
      if (fileData.products && fileData.products.length === 0) {
        errorMessage = 'The template file is empty. Please add at least one product to the file before uploading.';
      }
      // Check if this is the unmodified template with example data
      else if (fileData.products && fileData.products.some(product =>
        product.sku === 'PROD001' || product.sku === 'PROD002' || product.sku === 'PROD003'
      )) {
        errorMessage = 'Please replace the example data in the template with your own product data before uploading.';
      }
      // If there's a products sheet with data
      else if (fileData.products && fileData.products.length > 0) {
        const productHeaders = Object.keys(fileData.products[0]);
        const productHeadersLower = productHeaders.map(header => header.toLowerCase());
        const requiredProductHeaders = ['sku', 'name', 'category', 'type'];

        // More flexible matching - check if any header contains the required text
        const missingProductHeaders = [];
        for (const requiredHeader of requiredProductHeaders) {
          // Check if any header contains the required text (case insensitive)
          const found = productHeadersLower.some(header =>
            header.includes(requiredHeader.toLowerCase())
          );

          if (!found) {
            missingProductHeaders.push(requiredHeader);
          }
        }

        if (missingProductHeaders.length > 0) {
          errorMessage = `Missing required columns in the Products sheet: ${missingProductHeaders.join(', ')}. Please use the template file.`;
        } else {
          // If all required headers are present but there's no data, it's an empty template
          if (fileData.products.length === 1 && Object.values(fileData.products[0]).every(val => !val)) {
            errorMessage = 'The template file is empty. Please add at least one product to the file before uploading.';
          }
        }
      } else {
        errorMessage = 'No products found in the file or the Products sheet is missing. Please use the template file.';
      }

      ctx.body = {
        data: {
          valid: false,
          isTemplateValid: false,
          errors: [errorMessage],
          summary: { newProducts: 0, updateProducts: 0, totalVariants: 0 }
        }
      };
      return;
    }

    // Continue with normal validation
    const validationResult = validateProductData(fileData);
    console.log('Product validation result:', {
      valid: validationResult.valid,
      errorsCount: validationResult.errors ? validationResult.errors.length : 0,
      needsMapping: validationResult.needsMapping,
      summary: validationResult.summary
    });

    // Clean up the uploaded file
    fs.unlinkSync(file.path);

    ctx.body = {
      data: {
        ...validationResult,
        isTemplateValid: true
      }
    };
  } catch (err) {
    // Clean up the uploaded file in case of error
    if (fs.existsSync(file.path)) {
      fs.unlinkSync(file.path);
    }

    console.error('Error validating file:', err);
    throw new HttpError(400, `Error validating file: ${err.message}. Please ensure you are using the correct template format.`);
  }
}

// Controller: Import products from a file
async function importProducts(ctx) {
  const { Category, Product, SystemLog } = mongoose.models;
  const { file } = ctx.request;
  const { vendor } = ctx.state;
  const { fieldMapping } = ctx.request.body || {};

  if (!file) {
    throw new HttpError(400, 'No file uploaded');
  }

  // Create uploads directory if it doesn't exist
  const uploadDir = path.join(__dirname, '../../uploads');
  if (!fs.existsSync(uploadDir)) {
    fs.mkdirSync(uploadDir, { recursive: true });
  }

  try {
    // Parse the file into sheets
    const fileData = await parseFileWithSheets(file.path);

    // Validate that the file matches the expected template
    const isTemplateValid = validateTemplate(fileData);

    // If template validation fails, return early with an error
    if (!isTemplateValid) {
      // Clean up the uploaded file
      fs.unlinkSync(file.path);

      throw new HttpError(400, 'The file does not match the expected template format. Please download and use the template file.');
    }

    // Validate the data
    const validationResult = validateProductData(fileData, fieldMapping);

    if (!validationResult.valid) {
      // Clean up the uploaded file
      fs.unlinkSync(file.path);

      // If field mapping is needed, return that info
      if (validationResult.needsMapping) {
        ctx.body = {
          data: {
            needsMapping: true,
            sheets: validationResult.sheets,
            message: 'Field mapping required'
          }
        };
        return;
      }

      throw new HttpError(400, 'Validation failed', { errors: validationResult.errors });
    }

    const results = {
      success: [],
      errors: [],
      warnings: []
    };

    // Get the processed data with applied mapping
    const processedData = applyFieldMapping(fileData, fieldMapping);

    // Group products by SKU to handle variants and batches
    const productGroups = groupProductData(processedData);

    // Process in batches to avoid memory issues
    const batchSize = 10;
    const productSkus = Object.keys(productGroups);
    const totalBatches = Math.ceil(productSkus.length / batchSize);

    for (let batchIndex = 0; batchIndex < totalBatches; batchIndex++) {
      const batchSkus = productSkus.slice(batchIndex * batchSize, (batchIndex + 1) * batchSize);

      // Process each product in the current batch
      await Promise.all(batchSkus.map(async (sku) => {
        try {
          const productGroup = productGroups[sku];
          const productData = productGroup.product;

          // Find or create category
          let category;
          if (productData.category) {
            category = await Category.findOne({ name: productData.category });
            if (!category) {
              throw new Error(`Category "${productData.category}" not found`);
            }
          } else {
            throw new Error('Category is required');
          }

          // Process images if provided
          let mainImage = null;
          let additionalImages = [];

          if (productData.mainImageUrl) {
            try {
              mainImage = await processImageUrl(productData.mainImageUrl);
            } catch (imgErr) {
              results.warnings.push({
                sku: productData.sku,
                warning: `Failed to process main image: ${imgErr.message}`
              });
            }
          }

          if (productData.additionalImageUrls) {
            const imageUrls = productData.additionalImageUrls.split(',').map(url => url.trim()).filter(Boolean);

            for (const imageUrl of imageUrls) {
              try {
                const image = await processImageUrl(imageUrl);
                if (image) {
                  additionalImages.push(image);
                }
              } catch (imgErr) {
                results.warnings.push({
                  sku: productData.sku,
                  warning: `Failed to process additional image (${imageUrl}): ${imgErr.message}`
                });
              }
            }
          }

          // Process batches and variants
          const batches = [];

          if (productGroup.batches && productGroup.batches.length > 0) {
            for (const batchData of productGroup.batches) {
              // Process COA image if provided
              let coaImage = null;

              if (batchData.coaImageUrl) {
                try {
                  coaImage = await processImageUrl(batchData.coaImageUrl);
                } catch (imgErr) {
                  results.warnings.push({
                    sku: productData.sku,
                    batchId: batchData.batchId,
                    warning: `Failed to process COA image: ${imgErr.message}`
                  });
                }
              }

              // Find variants for this batch
              const batchVariants = productGroup.variants.filter(v =>
                v.batchId === batchData.batchId || (!v.batchId && batchData.batchId === productData.sku)
              );

              const variants = batchVariants.map(variant => ({
                sku: variant.sku,
                price: parseFloat(variant.price) || 0,
                quantity: parseInt(variant.quantity) || 0,
                weight: variant.weight || '',
                size: variant.size || '',
                custom1: variant.custom1 || '',
                custom2: variant.custom2 || '',
                deliveryPrice: parseFloat(variant.deliveryPrice) || 0,
                showProductNoInventory: true,
                showSoldOut: true
              }));

              batches.push({
                name: batchData.name || productData.name,
                batchID: batchData.batchId,
                quantity: parseInt(batchData.quantity) || 0,
                coa: coaImage ? [coaImage] : [],
                variants: variants.length > 0 ? variants : [{
                  sku: productData.sku,
                  price: 0,
                  quantity: 0,
                  showProductNoInventory: true,
                  showSoldOut: true
                }]
              });
            }
          } else if (productGroup.variants && productGroup.variants.length > 0) {
            // If no batches but we have variants, create a default batch
            const variants = productGroup.variants.map(variant => ({
              sku: variant.sku,
              price: parseFloat(variant.price) || 0,
              quantity: parseInt(variant.quantity) || 0,
              weight: variant.weight || '',
              size: variant.size || '',
              custom1: variant.custom1 || '',
              custom2: variant.custom2 || '',
              deliveryPrice: parseFloat(variant.deliveryPrice) || 0,
              showProductNoInventory: true,
              showSoldOut: true
            }));

            batches.push({
              name: productData.name,
              batchID: productData.sku,
              variants
            });
          } else {
            // No batches or variants, create default
            batches.push({
              name: productData.name,
              batchID: productData.sku,
              variants: [{
                sku: productData.sku,
                price: 0,
                quantity: 0,
                showProductNoInventory: true,
                showSoldOut: true
              }]
            });
          }

          // Prepare product data
          const payload = {
            mainSku: productData.sku,
            name: productData.name,
            description: productData.description || '',
            category: category._id,
            type: productData.type || 'hemp_derived_cbd',
            status: 'Inactive', // Always start as inactive
            batches,
            mainImage: mainImage || {},
            images: additionalImages || []
          };

          // Add strain if provided
          if (productData.strain) {
            payload.strain = productData.strain;
          }

          // Add content if provided
          if (productData.thcContent || productData.cbdContent) {
            payload.content = {};
            if (productData.thcContent) {
              payload.content.thc = productData.thcContent;
            }
            if (productData.cbdContent) {
              payload.content.cbd = productData.cbdContent;
            }
          }

          // Add return policy if provided
          if (productData.returnPolicy) {
            payload.returnPolicy = productData.returnPolicy;
          }

          // Check if product already exists
          const existingProduct = await Product.findOne({ mainSku: productData.sku, _vendor: vendor._id });

          if (existingProduct) {
            // Update existing product
            existingProduct.set(payload);
            await existingProduct.save();
            SystemLog.vendorProductUpdated(ctx, existingProduct, existingProduct);
            results.success.push({ sku: productData.sku, action: 'updated' });
          } else {
            // Create new product
            const product = await Product.create(payload, vendor);
            SystemLog.vendorProductCreated(ctx, product);
            results.success.push({ sku: productData.sku, action: 'created' });
          }
        } catch (err) {
          results.errors.push({ sku, error: err.message });
        }
      }));
    }

    // Clean up the uploaded file
    fs.unlinkSync(file.path);

    ctx.body = {
      data: results
    };
  } catch (err) {
    // Clean up the uploaded file in case of error
    if (fs.existsSync(file.path)) {
      fs.unlinkSync(file.path);
    }

    throw new HttpError(400, 'Error importing products: ' + err.message);
  }
}

// Helper function to parse the uploaded file into multiple sheets
async function parseFileWithSheets(filePath) {
  const extension = path.extname(filePath).toLowerCase();

  if (extension === '.csv') {
    // For CSV, we only have one sheet
    const products = await parseCSV(filePath);
    return {
      products: products,
      variants: [],
      batches: []
    };
  } else if (extension === '.xlsx' || extension === '.xls') {
    return parseExcelWithSheets(filePath);
  } else {
    throw new Error('Unsupported file format. Please use CSV or Excel (.xlsx, .xls) files.');
  }
}

// Helper function to parse a file (for backward compatibility)
async function parseFile(filePath) {
  const fileData = await parseFileWithSheets(filePath);
  return fileData.products;
}

// Parse CSV file
function parseCSV(filePath) {
  return new Promise((resolve, reject) => {
    const results = [];

    console.log('Parsing CSV file:', filePath);

    fs.createReadStream(filePath)
      .pipe(csv())
      .on('data', (data) => results.push(data))
      .on('end', () => {
        console.log('CSV parsing complete, rows found:', results.length);

        if (results.length > 0) {
          console.log('CSV headers:', Object.keys(results[0]));
          console.log('First row:', JSON.stringify(results[0]));
        } else {
          console.log('CSV file is empty or has only headers');
        }

        resolve(results);
      })
      .on('error', (err) => {
        console.error('Error parsing CSV:', err);
        reject(err);
      });
  });
}

// Parse Excel file with multiple sheets
function parseExcelWithSheets(filePath) {
  console.log('Parsing Excel file:', filePath);

  // Read the file with detailed options
  const workbook = xlsx.readFile(filePath, {
    cellFormula: false,
    cellHTML: false,
    cellText: true
  });

  console.log('Sheets found in workbook:', workbook.SheetNames);

  const result = {
    products: [],
    variants: [],
    batches: []
  };

  // Process each sheet
  workbook.SheetNames.forEach(sheetName => {
    console.log(`Processing sheet: ${sheetName}`);
    const worksheet = workbook.Sheets[sheetName];

    // Log the raw worksheet data for debugging
    console.log(`Raw worksheet data for ${sheetName} (first 200 chars):`,
      JSON.stringify(worksheet).substring(0, 200) + '...');

    // Get the range of the worksheet
    const range = xlsx.utils.decode_range(worksheet['!ref'] || 'A1');
    console.log(`Sheet ${sheetName} range:`, worksheet['!ref']);

    // Check if the sheet has any data beyond the header row
    const hasDataBeyondHeader = range.e.r > 0;
    console.log(`Sheet ${sheetName} has data beyond header: ${hasDataBeyondHeader}`);

    // Get the headers (first row)
    const headers = [];
    for (let col = range.s.c; col <= range.e.c; col++) {
      const cellAddress = xlsx.utils.encode_cell({r: 0, c: col});
      const cell = worksheet[cellAddress];
      if (cell && cell.v) {
        headers.push(cell.v);
      }
    }
    console.log(`Sheet ${sheetName} headers:`, headers);

    // Convert to JSON with header row
    let data = [];
    try {
      // Try with default options
      data = xlsx.utils.sheet_to_json(worksheet, { defval: '' });
      console.log(`Sheet ${sheetName} row count:`, data.length);

      if (data.length > 0) {
        console.log(`Sheet ${sheetName} first row:`, JSON.stringify(data[0]));
      } else {
        console.log(`Sheet ${sheetName} has no data rows`);
      }
    } catch (err) {
      console.error(`Error converting sheet ${sheetName} to JSON:`, err);
      data = [];
    }

    // Determine which data goes where based on sheet name
    const sheetNameLower = sheetName.toLowerCase();

    if (sheetNameLower.includes('product')) {
      result.products = data;
      console.log(`Assigned ${data.length} rows to products array`);
    } else if (sheetNameLower.includes('variant')) {
      result.variants = data;
      console.log(`Assigned ${data.length} rows to variants array`);
    } else if (sheetNameLower.includes('batch')) {
      result.batches = data;
      console.log(`Assigned ${data.length} rows to batches array`);
    } else if (sheetNameLower === 'instructions') {
      console.log('Skipping instructions sheet');
      // Skip instructions sheet
    } else if (result.products.length === 0) {
      // If no products sheet found yet, use the first non-instruction sheet
      result.products = data;
      console.log(`No products sheet found yet, using ${sheetName} with ${data.length} rows`);
    }
  });

  console.log('Final parsed data structure:', {
    productsCount: result.products.length,
    variantsCount: result.variants.length,
    batchesCount: result.batches.length
  });

  if (result.products.length > 0) {
    console.log('First product object:', JSON.stringify(result.products[0]));
  }

  return Promise.resolve(result);
}

// Validate the product data from all sheets
function validateProductData(fileData, fieldMapping) {
  const errors = [];
  const productRequiredFields = ['sku', 'name', 'category', 'type'];
  const variantRequiredFields = ['productSku', 'sku', 'price', 'quantity'];
  const batchRequiredFields = ['productSku', 'batchId', 'name'];

  // Check if there are any products
  if (!fileData.products || fileData.products.length === 0) {
    errors.push('No products found in the file');
    return {
      valid: false,
      errors,
      needsMapping: false,
      summary: { newProducts: 0, updateProducts: 0, totalVariants: 0 }
    };
  }

  // Check if field mapping is needed
  if (!fieldMapping) {
    // Check if all required fields exist in the data
    const productFields = Object.keys(fileData.products[0]);
    const variantFields = fileData.variants.length > 0 ? Object.keys(fileData.variants[0]) : [];
    const batchFields = fileData.batches.length > 0 ? Object.keys(fileData.batches[0]) : [];

    const missingProductFields = productRequiredFields.filter(field => !productFields.includes(field));
    const missingVariantFields = fileData.variants.length > 0 ?
      variantRequiredFields.filter(field => !variantFields.includes(field)) : [];
    const missingBatchFields = fileData.batches.length > 0 ?
      batchRequiredFields.filter(field => !batchFields.includes(field)) : [];

    if (missingProductFields.length > 0 || missingVariantFields.length > 0 || missingBatchFields.length > 0) {
      // Need field mapping
      return {
        valid: false,
        needsMapping: true,
        sheets: [
          {
            name: 'Products',
            type: 'products',
            columns: productFields.map(field => ({
              name: field,
              mappedTo: '',
              matched: false,
              sampleData: fileData.products[0][field]
            }))
          },
          ...(fileData.variants.length > 0 ? [{
            name: 'Variants',
            type: 'variants',
            columns: variantFields.map(field => ({
              name: field,
              mappedTo: '',
              matched: false,
              sampleData: fileData.variants[0][field]
            }))
          }] : []),
          ...(fileData.batches.length > 0 ? [{
            name: 'Batches',
            type: 'batches',
            columns: batchFields.map(field => ({
              name: field,
              mappedTo: '',
              matched: false,
              sampleData: fileData.batches[0][field]
            }))
          }] : [])
        ]
      };
    }
  }

  // Validate products
  fileData.products.forEach((product, index) => {
    const rowNum = index + 2; // +2 because of 0-indexing and header row

    // Check required fields
    productRequiredFields.forEach(field => {
      if (!product[field]) {
        errors.push(`Products sheet, Row ${rowNum}: Missing required field "${field}"`);
      }
    });

    // Validate type
    const validTypes = ['hemp_derived_cbd', 'flower_derived_cbd', 'thc', 'ancillary'];
    if (product.type && !validTypes.includes(product.type)) {
      errors.push(`Products sheet, Row ${rowNum}: Invalid product type "${product.type}". Valid types are: ${validTypes.join(', ')}`);
    }
  });

  // Validate variants
  fileData.variants.forEach((variant, index) => {
    const rowNum = index + 2;

    // Check required fields
    variantRequiredFields.forEach(field => {
      if (!variant[field]) {
        errors.push(`Variants sheet, Row ${rowNum}: Missing required field "${field}"`);
      }
    });

    // Validate price format
    if (variant.price && isNaN(parseFloat(variant.price))) {
      errors.push(`Variants sheet, Row ${rowNum}: Invalid price format`);
    }

    // Validate quantity format
    if (variant.quantity && isNaN(parseInt(variant.quantity))) {
      errors.push(`Variants sheet, Row ${rowNum}: Invalid quantity format`);
    }

    // Validate product SKU exists
    if (variant.productSku) {
      const productExists = fileData.products.some(p => p.sku === variant.productSku);
      if (!productExists) {
        errors.push(`Variants sheet, Row ${rowNum}: Product SKU "${variant.productSku}" not found in Products sheet`);
      }
    }
  });

  // Validate batches
  fileData.batches.forEach((batch, index) => {
    const rowNum = index + 2;

    // Check required fields
    batchRequiredFields.forEach(field => {
      if (!batch[field]) {
        errors.push(`Batches sheet, Row ${rowNum}: Missing required field "${field}"`);
      }
    });

    // Validate product SKU exists
    if (batch.productSku) {
      const productExists = fileData.products.some(p => p.sku === batch.productSku);
      if (!productExists) {
        errors.push(`Batches sheet, Row ${rowNum}: Product SKU "${batch.productSku}" not found in Products sheet`);
      }
    }
  });

  // Calculate summary
  const summary = {
    newProducts: fileData.products.length,
    updateProducts: 0, // This would be calculated by checking existing products
    totalVariants: fileData.variants.length || fileData.products.length // Use variants if available, otherwise assume one per product
  };

  return {
    valid: errors.length === 0,
    errors,
    needsMapping: false,
    summary
  };
}

// Apply field mapping to the data
function applyFieldMapping(fileData, fieldMapping) {
  if (!fieldMapping) {
    return fileData;
  }

  const result = {
    products: [],
    variants: [],
    batches: []
  };

  // Map product fields
  if (fieldMapping.products) {
    result.products = fileData.products.map(product => {
      const mappedProduct = {};

      Object.entries(fieldMapping.products).forEach(([originalField, mappedField]) => {
        if (mappedField && product[originalField] !== undefined) {
          mappedProduct[mappedField] = product[originalField];
        }
      });

      return mappedProduct;
    });
  } else {
    result.products = fileData.products;
  }

  // Map variant fields
  if (fieldMapping.variants) {
    result.variants = fileData.variants.map(variant => {
      const mappedVariant = {};

      Object.entries(fieldMapping.variants).forEach(([originalField, mappedField]) => {
        if (mappedField && variant[originalField] !== undefined) {
          mappedVariant[mappedField] = variant[originalField];
        }
      });

      return mappedVariant;
    });
  } else {
    result.variants = fileData.variants;
  }

  // Map batch fields
  if (fieldMapping.batches) {
    result.batches = fileData.batches.map(batch => {
      const mappedBatch = {};

      Object.entries(fieldMapping.batches).forEach(([originalField, mappedField]) => {
        if (mappedField && batch[originalField] !== undefined) {
          mappedBatch[mappedField] = batch[originalField];
        }
      });

      return mappedBatch;
    });
  } else {
    result.batches = fileData.batches;
  }

  return result;
}

// Group product data by SKU
function groupProductData(fileData) {
  const productGroups = {};

  // First, create groups for each product
  fileData.products.forEach(product => {
    productGroups[product.sku] = {
      product,
      variants: [],
      batches: []
    };
  });

  // Add variants to their respective products
  fileData.variants.forEach(variant => {
    if (productGroups[variant.productSku]) {
      productGroups[variant.productSku].variants.push(variant);
    }
  });

  // Add batches to their respective products
  fileData.batches.forEach(batch => {
    if (productGroups[batch.productSku]) {
      productGroups[batch.productSku].batches.push(batch);
    }
  });

  return productGroups;
}

// Validate that the uploaded file matches the expected template
function validateTemplate(fileData) {
  // Define the expected headers for each sheet
  const expectedProductHeaders = [
    'sku', 'name', 'description', 'category', 'type', 'strain',
    'thcContent', 'cbdContent', 'mainImageUrl', 'additionalImageUrls', 'returnPolicy'
  ];

  const expectedVariantHeaders = [
    'productSku', 'sku', 'price', 'quantity', 'weight', 'size',
    'custom1', 'custom2', 'deliveryPrice'
  ];

  const expectedBatchHeaders = [
    'productSku', 'batchId', 'name', 'quantity', 'coaImageUrl'
  ];

  // Check if products sheet exists
  if (!fileData.products) {
    console.log('Template validation failed: No products sheet found');
    return false;
  }

  // If the products sheet is empty (no data rows, just headers), that's actually valid for a template
  if (fileData.products.length === 0) {
    console.log('Empty template detected (products sheet has only headers)');
    return true;
  }

  // Check if this is the unmodified template with example data
  const isExampleTemplate = fileData.products.some(product =>
    product.sku === 'PROD001' || product.sku === 'PROD002' || product.sku === 'PROD003'
  );

  if (isExampleTemplate) {
    console.log('Detected unmodified template with example data');
    return false;
  }

  // Get the actual headers from the file
  const productHeaders = Object.keys(fileData.products[0]);
  console.log('Product headers found:', productHeaders);

  // Convert headers to lowercase for case-insensitive comparison
  const productHeadersLower = productHeaders.map(header => header.toLowerCase());
  console.log('Product headers lowercase:', productHeadersLower);

  // Check if at least the required product headers are present
  const requiredProductHeaders = ['sku', 'name', 'category', 'type'];
  console.log('Required product headers:', requiredProductHeaders);

  // More flexible matching - check if any header contains the required text
  const missingProductHeaders = [];
  for (const requiredHeader of requiredProductHeaders) {
    // Check if any header contains the required text (case insensitive)
    const found = productHeadersLower.some(header =>
      header.includes(requiredHeader.toLowerCase())
    );

    if (!found) {
      missingProductHeaders.push(requiredHeader);
    }
  }

  console.log('Missing product headers with flexible matching:', missingProductHeaders);

  if (missingProductHeaders.length > 0) {
    console.log('Missing required product headers:', missingProductHeaders);
    return false;
  }

  // Check if at least 60% of the expected headers are present (to allow for some flexibility)
  const matchingProductHeaders = [];
  for (const expectedHeader of expectedProductHeaders) {
    // Check if any header contains the expected text (case insensitive)
    const found = productHeadersLower.some(header =>
      header.includes(expectedHeader.toLowerCase())
    );

    if (found) {
      matchingProductHeaders.push(expectedHeader);
    }
  }

  const productHeadersMatch = matchingProductHeaders.length / expectedProductHeaders.length >= 0.6;

  console.log('Product headers match percentage:',
    (matchingProductHeaders.length / expectedProductHeaders.length * 100).toFixed(2) + '%',
    'Matching headers:', matchingProductHeaders);

  // If variants sheet exists, check its headers
  let variantHeadersMatch = true;
  if (fileData.variants && fileData.variants.length > 0) {
    const variantHeaders = Object.keys(fileData.variants[0]);
    console.log('Variant headers found:', variantHeaders);

    // Convert headers to lowercase for case-insensitive comparison
    const variantHeadersLower = variantHeaders.map(header => header.toLowerCase());
    console.log('Variant headers lowercase:', variantHeadersLower);

    const requiredVariantHeaders = ['productSku', 'sku', 'price', 'quantity'];
    console.log('Required variant headers:', requiredVariantHeaders);

    // More flexible matching - check if any header contains the required text
    const missingVariantHeaders = [];
    for (const requiredHeader of requiredVariantHeaders) {
      // Check if any header contains the required text (case insensitive)
      const found = variantHeadersLower.some(header => {
        // Special case for productSku which might be "product sku" or "product-sku"
        if (requiredHeader.toLowerCase() === 'productsku') {
          return header.includes('product') && header.includes('sku');
        }
        return header.includes(requiredHeader.toLowerCase());
      });

      if (!found) {
        missingVariantHeaders.push(requiredHeader);
      }
    }

    console.log('Missing variant headers with flexible matching:', missingVariantHeaders);

    if (missingVariantHeaders.length > 0) {
      console.log('Missing required variant headers:', missingVariantHeaders);
      variantHeadersMatch = false;
    }
  }

  // If batches sheet exists, check its headers
  let batchHeadersMatch = true;
  if (fileData.batches && fileData.batches.length > 0) {
    const batchHeaders = Object.keys(fileData.batches[0]);
    console.log('Batch headers found:', batchHeaders);

    // Convert headers to lowercase for case-insensitive comparison
    const batchHeadersLower = batchHeaders.map(header => header.toLowerCase());
    console.log('Batch headers lowercase:', batchHeadersLower);

    const requiredBatchHeaders = ['productSku', 'batchId', 'name'];
    console.log('Required batch headers:', requiredBatchHeaders);

    // More flexible matching - check if any header contains the required text
    const missingBatchHeaders = [];
    for (const requiredHeader of requiredBatchHeaders) {
      // Check if any header contains the required text (case insensitive)
      const found = batchHeadersLower.some(header => {
        // Special case for productSku which might be "product sku" or "product-sku"
        if (requiredHeader.toLowerCase() === 'productsku') {
          return header.includes('product') && header.includes('sku');
        }
        // Special case for batchId which might be "batch id" or "batch-id"
        if (requiredHeader.toLowerCase() === 'batchid') {
          return header.includes('batch') && (header.includes('id') || header.includes('number'));
        }
        return header.includes(requiredHeader.toLowerCase());
      });

      if (!found) {
        missingBatchHeaders.push(requiredHeader);
      }
    }

    console.log('Missing batch headers with flexible matching:', missingBatchHeaders);

    if (missingBatchHeaders.length > 0) {
      console.log('Missing required batch headers:', missingBatchHeaders);
      batchHeadersMatch = false;
    }
  }

  // The template is valid if the product headers match and any existing variant/batch headers match
  const isValid = productHeadersMatch && variantHeadersMatch && batchHeadersMatch;
  console.log('Template validation result:', isValid);
  return isValid;
}

// Process an image URL and return a file object
async function processImageUrl(url) {
  try {
    // This is a placeholder for actual image processing
    // In a real implementation, this would:
    // 1. Download the image from the URL
    // 2. Upload it to Cloudinary or similar service
    // 3. Return the file object in the format expected by the system

    // For now, we'll return a mock file object
    return {
      name: path.basename(url),
      uuid: `mock-uuid-${Date.now()}-${Math.floor(Math.random() * 1000)}`,
      mimeType: url.toLowerCase().endsWith('.png') ? 'image/png' : 'image/jpeg',
      size: 1024,
      isImage: true,
      isStored: true,
      cdnUrl: url,
      originalUrl: url
    };
  } catch (err) {
    throw new Error(`Failed to process image: ${err.message}`);
  }
}
