#!/bin/bash

# Script to manage MailDev Docker container

function start_maildev() {
    echo "Starting MailDev Docker container..."

    # Check if container already exists
    if docker ps -a --format '{{.Names}}' | grep -q '^maildev$'; then
        # Container exists, check if it's running
        if docker ps --format '{{.Names}}' | grep -q '^maildev$'; then
            echo "MailDev container is already running."
            echo "Web interface: http://localhost:1080"
            echo "SMTP server: localhost:1025"
        else
            # Container exists but not running, start it
            docker start maildev
            echo "MailDev container started."
            echo "Web interface: http://localhost:1080"
            echo "SMTP server: localhost:1025"
        fi
    else
        # Container doesn't exist, create and start it
        docker run -d --name maildev -p 1080:80 -p 1025:25 djfarrelly/maildev
        echo "MailDev container created and started."
        echo "Web interface: http://localhost:1080"
        echo "SMTP server: localhost:1025"
    fi
}

function stop_maildev() {
    echo "Stopping MailDev Docker container..."

    # Check if container exists and is running
    if docker ps --format '{{.Names}}' | grep -q '^maildev$'; then
        docker stop maildev
        echo "MailDev container stopped."
    else
        echo "MailDev container is not running."
    fi
}

function remove_maildev() {
    echo "Removing MailDev Docker container..."

    # Check if container exists
    if docker ps -a --format '{{.Names}}' | grep -q '^maildev$'; then
        # Stop if running
        if docker ps --format '{{.Names}}' | grep -q '^maildev$'; then
            docker stop maildev
        fi
        # Remove container
        docker rm maildev
        echo "MailDev container removed."
    else
        echo "MailDev container does not exist."
    fi
}

# Process command line arguments
case "$1" in
    start)
        start_maildev
        ;;
    stop)
        stop_maildev
        ;;
    restart)
        stop_maildev
        start_maildev
        ;;
    remove)
        remove_maildev
        ;;
    *)
        echo "Usage: $0 {start|stop|restart|remove}"
        exit 1
        ;;
esac

exit 0
