<template>
  <div class="modal-dialog modal-lg">
    <div class="modal-content">
      <div class="modal-header">
        <button type="button" class="close" @click="$emit('close')">
          <span aria-hidden="true">&times;</span>
        </button>
        <h4 class="modal-title">Map Spreadsheet Fields</h4>
      </div>
      <div class="modal-body">
        <div class="alert alert-info mb-4">
          <div class="d-flex">
            <div class="mr-3">
              <svg class="icon icon-lg" viewBox="0 0 24 24">
                <path d="M11 17H13V11H11V17ZM12 2C6.48 2 2 6.48 2 12S6.48 22 12 22 22 17.52 22 12 17.52 2 12 2ZM12 20C7.59 20 4 16.41 4 12S7.59 4 12 4 20 7.59 20 12 16.41 20 12 20ZM11 9H13V7H11V9Z" fill-rule="evenodd"></path>
              </svg>
            </div>
            <div>
              <p>Some fields in your spreadsheet don't match our expected format. Please map your spreadsheet columns to our system fields.</p>
            </div>
          </div>
        </div>

        <div v-if="loading" class="text-center p-5">
          <div class="spinner-border text-primary" role="status">
            <span class="sr-only">Loading...</span>
          </div>
          <p class="mt-3">Analyzing your spreadsheet...</p>
        </div>

        <div v-else>
          <div v-if="sheets.length > 1" class="mb-4">
            <ul class="nav nav-tabs">
              <li v-for="(sheet, index) in sheets" :key="index" :class="{ 'active': activeSheet === index }">
                <a href="#" @click.prevent="activeSheet = index">{{ sheet.name }}</a>
              </li>
            </ul>
          </div>

          <div v-for="(sheet, sheetIndex) in sheets" :key="'sheet-' + sheetIndex" v-show="activeSheet === sheetIndex">
            <h5 class="mb-3">{{ sheet.name }} Sheet</h5>
            
            <div class="table-responsive">
              <table class="table table-bordered">
                <thead>
                  <tr>
                    <th style="width: 40%">Your Column</th>
                    <th style="width: 40%">Our Field</th>
                    <th style="width: 20%">Required</th>
                  </tr>
                </thead>
                <tbody>
                  <tr v-for="(column, index) in sheet.columns" :key="'column-' + index">
                    <td>
                      <div class="d-flex align-items-center">
                        <div class="mr-2">
                          <span class="badge" :class="column.matched ? 'badge-success' : 'badge-warning'">
                            {{ column.matched ? 'Matched' : 'Unmatched' }}
                          </span>
                        </div>
                        <div>{{ column.name }}</div>
                      </div>
                      <div v-if="column.sampleData" class="text-muted small mt-1">
                        Sample: {{ column.sampleData }}
                      </div>
                    </td>
                    <td>
                      <div class="form-control-select">
                        <select class="form-control" v-model="column.mappedTo">
                          <option value="">-- Not Mapped --</option>
                          <option v-for="field in availableFields[sheet.type]" :key="field.name" :value="field.name">
                            {{ field.label }}
                          </option>
                        </select>
                        <span class="form-control-select-arrow">
                          <span class="caret"></span>
                        </span>
                      </div>
                    </td>
                    <td class="text-center">
                      <span v-if="isRequiredField(sheet.type, column.mappedTo)" class="text-danger">
                        <svg class="icon" viewBox="0 0 24 24">
                          <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm0 18c-4.41 0-8-3.59-8-8s3.59-8 8-8 8 3.59 8 8-3.59 8-8 8zm-1-13h2v6h-2zm0 8h2v2h-2z" fill-rule="evenodd"></path>
                        </svg>
                        Required
                      </span>
                      <span v-else>Optional</span>
                    </td>
                  </tr>
                </tbody>
              </table>
            </div>

            <div v-if="getMissingRequiredFields(sheet.type).length > 0" class="alert alert-danger mt-3">
              <h6>Missing Required Fields:</h6>
              <ul class="mb-0">
                <li v-for="field in getMissingRequiredFields(sheet.type)" :key="field.name">
                  {{ field.label }}
                </li>
              </ul>
            </div>
          </div>
        </div>
      </div>
      <div class="modal-footer">
        <button type="button" class="btn btn-default" @click="$emit('close')">
          Cancel
        </button>
        <button 
          type="button" 
          class="btn btn-primary" 
          :disabled="!isValid || loading" 
          @click="applyMapping"
        >
          Apply Mapping
        </button>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'FieldMappingModal',
  props: {
    fileData: {
      type: Object,
      required: true
    }
  },
  data() {
    return {
      loading: true,
      activeSheet: 0,
      sheets: [],
      availableFields: {
        products: [
          { name: 'sku', label: 'SKU', required: true },
          { name: 'name', label: 'Name', required: true },
          { name: 'description', label: 'Description', required: false },
          { name: 'category', label: 'Category', required: true },
          { name: 'type', label: 'Type', required: true },
          { name: 'status', label: 'Status', required: false },
          { name: 'strain', label: 'Strain', required: false },
          { name: 'thcContent', label: 'THC Content (%)', required: false },
          { name: 'cbdContent', label: 'CBD Content (%)', required: false },
          { name: 'returnPolicy', label: 'Return Policy', required: false },
          { name: 'mainImageUrl', label: 'Main Image URL', required: false },
          { name: 'additionalImageUrls', label: 'Additional Image URLs', required: false }
        ],
        variants: [
          { name: 'productSku', label: 'Product SKU', required: true },
          { name: 'sku', label: 'Variant SKU', required: true },
          { name: 'price', label: 'Price', required: true },
          { name: 'quantity', label: 'Quantity', required: true },
          { name: 'weight', label: 'Weight', required: false },
          { name: 'size', label: 'Size', required: false },
          { name: 'custom1', label: 'Custom1', required: false },
          { name: 'custom2', label: 'Custom2', required: false },
          { name: 'deliveryPrice', label: 'Delivery Price', required: false }
        ],
        batches: [
          { name: 'productSku', label: 'Product SKU', required: true },
          { name: 'batchId', label: 'Batch ID', required: true },
          { name: 'name', label: 'Batch Name', required: true },
          { name: 'coaImageUrl', label: 'COA Image URL', required: false },
          { name: 'quantity', label: 'Quantity', required: false }
        ]
      }
    };
  },
  computed: {
    isValid() {
      // Check if all sheets have all required fields mapped
      return this.sheets.every(sheet => this.getMissingRequiredFields(sheet.type).length === 0);
    }
  },
  mounted() {
    this.analyzeFileData();
  },
  methods: {
    analyzeFileData() {
      this.loading = true;
      
      // In a real implementation, this would analyze the actual file data
      // For now, we'll simulate some sheets with columns
      
      setTimeout(() => {
        // Simulate product sheet
        const productSheet = {
          name: 'Products',
          type: 'products',
          columns: [
            { name: 'Product ID', mappedTo: 'sku', matched: false, sampleData: 'PROD001' },
            { name: 'Product Name', mappedTo: 'name', matched: false, sampleData: 'Example Product 1' },
            { name: 'Details', mappedTo: 'description', matched: false, sampleData: 'This is a description' },
            { name: 'Product Category', mappedTo: 'category', matched: false, sampleData: 'Hemp derived CBD' },
            { name: 'Product Type', mappedTo: 'type', matched: false, sampleData: 'hemp_derived_cbd' },
            { name: 'Image Link', mappedTo: 'mainImageUrl', matched: false, sampleData: 'https://example.com/image.jpg' }
          ]
        };
        
        // Simulate variants sheet
        const variantsSheet = {
          name: 'Variants',
          type: 'variants',
          columns: [
            { name: 'Parent SKU', mappedTo: 'productSku', matched: false, sampleData: 'PROD001' },
            { name: 'SKU', mappedTo: 'sku', matched: false, sampleData: 'PROD001-S' },
            { name: 'Retail Price', mappedTo: 'price', matched: false, sampleData: '29.99' },
            { name: 'Stock', mappedTo: 'quantity', matched: false, sampleData: '100' },
            { name: 'Product Weight', mappedTo: 'weight', matched: false, sampleData: '30ml' },
            { name: 'Product Size', mappedTo: 'size', matched: false, sampleData: 'Small' }
          ]
        };
        
        // Auto-match columns that have similar names to our fields
        this.autoMatchColumns(productSheet);
        this.autoMatchColumns(variantsSheet);
        
        this.sheets = [productSheet, variantsSheet];
        this.loading = false;
      }, 1000);
    },
    
    autoMatchColumns(sheet) {
      const fields = this.availableFields[sheet.type];
      
      sheet.columns.forEach(column => {
        // Try to find a matching field
        const matchedField = fields.find(field => {
          const columnNameLower = column.name.toLowerCase();
          const fieldNameLower = field.name.toLowerCase();
          const fieldLabelLower = field.label.toLowerCase();
          
          return columnNameLower === fieldNameLower || 
                 columnNameLower === fieldLabelLower ||
                 columnNameLower.includes(fieldNameLower) ||
                 fieldNameLower.includes(columnNameLower);
        });
        
        if (matchedField) {
          column.mappedTo = matchedField.name;
          column.matched = true;
        }
      });
    },
    
    isRequiredField(sheetType, fieldName) {
      if (!fieldName) return false;
      
      const field = this.availableFields[sheetType].find(f => f.name === fieldName);
      return field && field.required;
    },
    
    getMissingRequiredFields(sheetType) {
      const requiredFields = this.availableFields[sheetType].filter(field => field.required);
      const mappedFields = this.sheets
        .find(s => s.type === sheetType)
        .columns
        .map(column => column.mappedTo);
      
      return requiredFields.filter(field => !mappedFields.includes(field.name));
    },
    
    applyMapping() {
      // Create a mapping object to send back
      const mapping = {};
      
      this.sheets.forEach(sheet => {
        mapping[sheet.type] = {};
        
        sheet.columns.forEach(column => {
          if (column.mappedTo) {
            mapping[sheet.type][column.name] = column.mappedTo;
          }
        });
      });
      
      this.$emit('mapping-applied', mapping);
    }
  }
};
</script>
