'use strict';

const { Client } = require('@hubspot/api-client');
const config = require('./config');
const { AppError } = require('./error');

let hubspotClient;

// Rate limiting
const rateLimiter = {
    requests: [],
    maxRequests: 10, // per second
    windowMs: 1000,
    
    canMakeRequest() {
        const now = Date.now();
        this.requests = this.requests.filter(time => now - time < this.windowMs);
        return this.requests.length < this.maxRequests;
    },
    
    recordRequest() {
        this.requests.push(Date.now());
    },
    
    async waitForRateLimit() {
        if (!this.canMakeRequest()) {
            const oldestRequest = Math.min(...this.requests);
            const waitTime = this.windowMs - (Date.now() - oldestRequest);
            if (waitTime > 0) {
                await new Promise(resolve => setTimeout(resolve, waitTime));
            }
        }
    }
};

// Initialize HubSpot client
function init() {
    if (!config.hubspot || !config.hubspot.accessToken) {
        console.warn('HubSpot access token not configured');
        return;
    }
    
    hubspotClient = new Client({
        accessToken: config.hubspot.accessToken
    });
    
    console.log('HubSpot client initialized');
}

// Create or update contact in HubSpot
async function createOrUpdateContact(userData) {
    // Input validation
    if (!userData || typeof userData !== 'object') {
        throw new AppError(422, 'User data is required');
    }

    if (!userData.email || typeof userData.email !== 'string') {
        throw new AppError(422, 'Valid email is required');
    }

    // Validate email format
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(userData.email)) {
        throw new AppError(422, 'Invalid email format');
    }

    if (!hubspotClient) {
        throw new AppError(503, 'HubSpot service not available');
    }

    await rateLimiter.waitForRateLimit();
    rateLimiter.recordRequest();

    try {
        const contactProperties = buildContactProperties(userData);

        // Validate that we have at least some properties to sync
        if (!contactProperties || Object.keys(contactProperties).length === 0) {
            throw new AppError(422, 'No valid contact properties to sync');
        }

        // Try to find existing contact by email
        let existingContact = null;
        try {
            const searchResponse = await hubspotClient.crm.contacts.searchApi.doSearch({
                filterGroups: [{
                    filters: [{
                        propertyName: 'email',
                        operator: 'EQ',
                        value: userData.email
                    }]
                }],
                properties: ['email', 'firstname', 'lastname', 'canideal_user_id'],
                limit: 1
            });

            if (searchResponse?.results && Array.isArray(searchResponse.results) && searchResponse.results.length > 0) {
                existingContact = searchResponse.results[0];
            }
        } catch (searchError) {
            console.warn('Error searching for existing contact:', searchError.message);
            // Continue with creation attempt rather than failing
        }
        
        let contact;
        if (existingContact) {
            // Update existing contact
            contact = await hubspotClient.crm.contacts.basicApi.update(
                existingContact.id,
                { properties: contactProperties }
            );
            console.log(`Updated HubSpot contact: ${userData.email}`);
        } else {
            // Create new contact
            contact = await hubspotClient.crm.contacts.basicApi.create({
                properties: contactProperties
            });
            console.log(`Created HubSpot contact: ${userData.email}`);
        }
        
        return {
            hubspotContactId: contact.id,
            isNew: !existingContact,
            properties: contact.properties
        };
        
    } catch (error) {
        console.error('HubSpot contact creation/update error:', error);
        
        if (error.code === 429) {
            throw new AppError(429, 'HubSpot rate limit exceeded');
        } else if (error.code === 401) {
            throw new AppError(503, 'HubSpot authentication error');
        } else {
            throw new AppError(500, `HubSpot contact sync failed: ${error.message}`);
        }
    }
}

// Build contact properties from user data
function buildContactProperties(userData) {
    if (!userData || typeof userData !== 'object') {
        return {};
    }

    // Safely extract name parts
    let firstName = '';
    let lastName = '';

    if (userData.firstName && typeof userData.firstName === 'string') {
        firstName = userData.firstName.trim().substring(0, 50);
    } else if (userData.name && typeof userData.name === 'string') {
        const nameParts = userData.name.trim().split(' ');
        firstName = nameParts[0] || '';
        lastName = nameParts.slice(1).join(' ') || '';
    }

    if (userData.lastName && typeof userData.lastName === 'string') {
        lastName = userData.lastName.trim().substring(0, 50);
    }

    const properties = {
        email: sanitizeString(userData.email, 100),
        firstname: firstName,
        lastname: lastName,
        phone: sanitizeString(userData.phone, 20),
        company: sanitizeString(userData.businessName || userData.storeName, 100),
        website: sanitizeUrl(userData.website),
        lifecyclestage: 'lead'
    };

    // Add custom CanIDeal properties with validation
    const customProps = config.hubspot?.customProperties;
    if (customProps && typeof customProps === 'object') {
        // Safely add custom properties with validation
        if (customProps.canideal_user_id) {
            const userId = userData.userId || userData._id;
            if (userId) {
                properties[customProps.canideal_user_id] = sanitizeString(userId.toString(), 50);
            }
        }

        if (customProps.canideal_user_role) {
            const userRole = userData.userRole || userData.role;
            if (userRole && ['vendor', 'retailer', 'admin'].includes(userRole)) {
                properties[customProps.canideal_user_role] = userRole;
            }
        }

        if (customProps.canideal_signup_date) {
            const signupDate = userData.createdAt;
            if (signupDate) {
                properties[customProps.canideal_signup_date] = formatDate(signupDate);
            }
        }

        if (customProps.canideal_application_status) {
            const status = userData.applicationStatus;
            if (status && ['pending', 'approved', 'rejected'].includes(status)) {
                properties[customProps.canideal_application_status] = status;
            }
        }

        if (customProps.canideal_business_type) {
            properties[customProps.canideal_business_type] = sanitizeString(userData.businessType, 50);
        }

        if (customProps.canideal_state) {
            properties[customProps.canideal_state] = sanitizeString(userData.state, 20);
        }

        if (customProps.canideal_license_number) {
            properties[customProps.canideal_license_number] = sanitizeString(userData.licenseNumber, 50);
        }

        if (customProps.canideal_is_domestic) {
            properties[customProps.canideal_is_domestic] = userData.isDomestic === true ? 'true' : 'false';
        }

        if (customProps.canideal_last_login) {
            const lastLogin = userData.lastLogin;
            if (lastLogin) {
                properties[customProps.canideal_last_login] = formatDate(lastLogin);
            }
        }

        if (customProps.canideal_product_count) {
            const productCount = parseInt(userData.productCount);
            if (!isNaN(productCount) && productCount >= 0) {
                properties[customProps.canideal_product_count] = productCount;
            }
        }

        if (customProps.canideal_order_count) {
            const orderCount = parseInt(userData.orderCount);
            if (!isNaN(orderCount) && orderCount >= 0) {
                properties[customProps.canideal_order_count] = orderCount;
            }
        }

        if (customProps.canideal_total_revenue) {
            const totalRevenue = parseFloat(userData.totalRevenue);
            if (!isNaN(totalRevenue) && totalRevenue >= 0) {
                properties[customProps.canideal_total_revenue] = totalRevenue;
            }
        }
    }

    // Remove empty, null, or undefined properties
    Object.keys(properties).forEach(key => {
        const value = properties[key];
        if (value === '' || value === null || value === undefined) {
            delete properties[key];
        }
    });

    return properties;
}

// Helper function to sanitize strings
function sanitizeString(value, maxLength = 100) {
    if (!value || typeof value !== 'string') {
        return '';
    }

    return value
        .trim()
        .replace(/[<>]/g, '') // Remove HTML tags
        .replace(/[\r\n\t]/g, ' ') // Replace line breaks with spaces
        .replace(/\s+/g, ' ') // Normalize whitespace
        .substring(0, maxLength);
}

// Helper function to sanitize URLs
function sanitizeUrl(url) {
    if (!url || typeof url !== 'string') {
        return '';
    }

    const trimmed = url.trim();
    if (trimmed.length === 0) {
        return '';
    }

    // Add protocol if missing
    if (!trimmed.startsWith('http://') && !trimmed.startsWith('https://')) {
        return `https://${trimmed}`;
    }

    return trimmed.substring(0, 200);
}

// Helper function to format dates
function formatDate(date) {
    if (!date) {
        return '';
    }

    try {
        const dateObj = date instanceof Date ? date : new Date(date);
        if (isNaN(dateObj.getTime())) {
            return '';
        }
        return dateObj.toISOString();
    } catch (error) {
        console.warn('Error formatting date:', error);
        return '';
    }
}

// Add contact to specific list
async function addContactToList(contactId, listId) {
    if (!hubspotClient || !listId) {
        return false;
    }
    
    await rateLimiter.waitForRateLimit();
    rateLimiter.recordRequest();
    
    try {
        await hubspotClient.crm.lists.membershipsApi.add(listId, {
            objectIds: [contactId]
        });
        console.log(`Added contact ${contactId} to list ${listId}`);
        return true;
    } catch (error) {
        console.error('Error adding contact to list:', error);
        return false;
    }
}

// Trigger workflow for contact
async function triggerWorkflow(contactId, workflowId) {
    if (!hubspotClient || !workflowId) {
        return false;
    }
    
    await rateLimiter.waitForRateLimit();
    rateLimiter.recordRequest();
    
    try {
        // Note: Workflow enrollment via API requires specific HubSpot setup
        // This is a placeholder for the actual workflow trigger implementation
        console.log(`Would trigger workflow ${workflowId} for contact ${contactId}`);
        
        // In practice, you might use:
        // - Workflow enrollment API (if available in your HubSpot tier)
        // - Custom property updates that trigger workflows
        // - Webhook notifications to HubSpot
        
        return true;
    } catch (error) {
        console.error('Error triggering workflow:', error);
        return false;
    }
}

// Send email using HubSpot template
async function sendTemplateEmail(contactId, templateId, customProperties = {}) {
    if (!hubspotClient || !templateId) {
        return false;
    }
    
    await rateLimiter.waitForRateLimit();
    rateLimiter.recordRequest();
    
    try {
        const emailData = {
            emailId: templateId,
            message: {
                to: [contactId],
                from: config.hubspot.defaultFromEmail || '<EMAIL>'
            },
            customProperties
        };
        
        // Note: This is a simplified version - actual HubSpot email API may differ
        console.log(`Would send template email ${templateId} to contact ${contactId}`);
        return true;
    } catch (error) {
        console.error('Error sending template email:', error);
        return false;
    }
}

// Sync user data to HubSpot with appropriate list and workflow triggers
async function syncUserToHubSpot(userData, triggerType = 'signup') {
    try {
        // Create or update contact
        const contactResult = await createOrUpdateContact(userData);
        
        if (!contactResult.hubspotContactId) {
            throw new Error('Failed to get HubSpot contact ID');
        }
        
        const contactId = contactResult.hubspotContactId;
        const isNewContact = contactResult.isNew;
        
        // Add to appropriate lists based on user role
        const lists = config.hubspot.lists;
        if (lists) {
            if (userData.userRole === 'vendor' && lists.vendors) {
                await addContactToList(contactId, lists.vendors);
            } else if (userData.userRole === 'retailer' && lists.retailers) {
                await addContactToList(contactId, lists.retailers);
            }
            
            // Add to domestic/international lists
            if (userData.isDomestic && lists.domesticUsers) {
                await addContactToList(contactId, lists.domesticUsers);
            } else if (!userData.isDomestic && lists.internationalUsers) {
                await addContactToList(contactId, lists.internationalUsers);
            }
        }
        
        // Trigger appropriate workflows based on trigger type
        const workflows = config.hubspot.workflows;
        if (workflows && isNewContact) {
            if (triggerType === 'signup') {
                if (userData.userRole === 'vendor' && workflows.vendorOnboarding) {
                    await triggerWorkflow(contactId, workflows.vendorOnboarding);
                } else if (userData.userRole === 'retailer' && workflows.retailerOnboarding) {
                    await triggerWorkflow(contactId, workflows.retailerOnboarding);
                }
            } else if (triggerType === 'application_approved' && workflows.applicationApproved) {
                await triggerWorkflow(contactId, workflows.applicationApproved);
            } else if (triggerType === 'first_product' && workflows.firstProductListed) {
                await triggerWorkflow(contactId, workflows.firstProductListed);
            } else if (triggerType === 'first_order' && workflows.firstOrderPlaced) {
                await triggerWorkflow(contactId, workflows.firstOrderPlaced);
            }
        }
        
        return {
            success: true,
            contactId,
            isNew: isNewContact,
            triggerType
        };
        
    } catch (error) {
        console.error('HubSpot sync error:', error);
        return {
            success: false,
            error: error.message,
            triggerType
        };
    }
}

// Get HubSpot sync statistics
async function getSyncStats() {
    if (!hubspotClient) {
        return { error: 'HubSpot not configured' };
    }
    
    try {
        // This would typically query your database for sync statistics
        // For now, return placeholder data
        return {
            totalContacts: 0,
            syncedToday: 0,
            lastSyncTime: new Date(),
            errorRate: 0,
            rateLimitHits: 0
        };
    } catch (error) {
        console.error('Error getting sync stats:', error);
        return { error: error.message };
    }
}

module.exports = {
    init,
    createOrUpdateContact,
    addContactToList,
    triggerWorkflow,
    sendTemplateEmail,
    syncUserToHubSpot,
    getSyncStats
};
