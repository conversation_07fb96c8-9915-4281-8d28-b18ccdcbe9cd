{{> header}}

<tr>
    <td class="wrapper" style="font-family: sans-serif; font-size: 14px; vertical-align: top; box-sizing: border-box; padding: 20px;" valign="top">
        <table border="0" cellpadding="0" cellspacing="0" style="border-collapse: separate; mso-table-lspace: 0pt; mso-table-rspace: 0pt; width: 100%;" width="100%">
            <tr>
                <td style="font-family: sans-serif; font-size: 14px; vertical-align: top;" valign="top">
                    <h2 style="color: #000000; font-family: sans-serif; font-weight: 300; line-height: 1.4; margin: 0; margin-bottom: 30px; font-size: 24px; text-align: center; text-transform: capitalize;">New User Signup</h2>
                    
                    <p style="font-family: sans-serif; font-size: 14px; font-weight: normal; margin: 0; margin-bottom: 15px;">A new user has signed up for CanIDeal:</p>
                    
                    <table border="0" cellpadding="0" cellspacing="0" style="border-collapse: separate; mso-table-lspace: 0pt; mso-table-rspace: 0pt; width: 100%; background-color: #f6f6f6; border-radius: 5px; margin-bottom: 20px;" width="100%">
                        <tr>
                            <td style="font-family: sans-serif; font-size: 14px; vertical-align: top; padding: 20px;" valign="top">
                                <h3 style="color: #000000; font-family: sans-serif; font-weight: 400; line-height: 1.4; margin: 0; margin-bottom: 15px; font-size: 18px;">User Information</h3>
                                
                                <p style="font-family: sans-serif; font-size: 14px; font-weight: normal; margin: 0; margin-bottom: 10px;"><strong>Name:</strong> {{user.name}}</p>
                                <p style="font-family: sans-serif; font-size: 14px; font-weight: normal; margin: 0; margin-bottom: 10px;"><strong>Email:</strong> {{user.email}}</p>
                                {{#if user.phone}}
                                <p style="font-family: sans-serif; font-size: 14px; font-weight: normal; margin: 0; margin-bottom: 10px;"><strong>Phone:</strong> {{user.phone}}</p>
                                {{/if}}
                                <p style="font-family: sans-serif; font-size: 14px; font-weight: normal; margin: 0; margin-bottom: 10px;"><strong>Account Type:</strong> {{accountType}}</p>
                                
                                {{#if retailer}}
                                <h4 style="color: #000000; font-family: sans-serif; font-weight: 400; line-height: 1.4; margin: 15px 0 10px 0; font-size: 16px;">Retailer Information</h4>
                                <p style="font-family: sans-serif; font-size: 14px; font-weight: normal; margin: 0; margin-bottom: 10px;"><strong>Business Name:</strong> {{retailer.businessName}}</p>
                                {{#if retailer.retailerBudget}}
                                <p style="font-family: sans-serif; font-size: 14px; font-weight: normal; margin: 0; margin-bottom: 10px;"><strong>Budget:</strong> {{retailer.retailerBudget}}</p>
                                {{/if}}
                                {{/if}}
                                
                                {{#if vendor}}
                                <h4 style="color: #000000; font-family: sans-serif; font-weight: 400; line-height: 1.4; margin: 15px 0 10px 0; font-size: 16px;">Vendor Information</h4>
                                <p style="font-family: sans-serif; font-size: 14px; font-weight: normal; margin: 0; margin-bottom: 10px;"><strong>Store Name:</strong> {{vendor.storeName}}</p>
                                {{#if vendor.vendorProductCount}}
                                <p style="font-family: sans-serif; font-size: 14px; font-weight: normal; margin: 0; margin-bottom: 10px;"><strong>Product Count:</strong> {{vendor.vendorProductCount}}</p>
                                {{/if}}
                                {{#if vendor.isBroker}}
                                <p style="font-family: sans-serif; font-size: 14px; font-weight: normal; margin: 0; margin-bottom: 10px;"><strong>Is Broker:</strong> Yes</p>
                                {{/if}}
                                {{/if}}
                            </td>
                        </tr>
                    </table>
                    
                    <table border="0" cellpadding="0" cellspacing="0" class="btn btn-primary" style="border-collapse: separate; mso-table-lspace: 0pt; mso-table-rspace: 0pt; width: 100%; box-sizing: border-box;" width="100%">
                        <tbody>
                            <tr>
                                <td align="center" style="font-family: sans-serif; font-size: 14px; vertical-align: top; padding-bottom: 15px;" valign="top">
                                    <table border="0" cellpadding="0" cellspacing="0" style="border-collapse: separate; mso-table-lspace: 0pt; mso-table-rspace: 0pt; width: auto;">
                                        <tbody>
                                            <tr>
                                                <td style="font-family: sans-serif; font-size: 14px; vertical-align: top; background-color: #3498db; border-radius: 5px; text-align: center;" valign="top" bgcolor="#3498db" align="center">
                                                    <a href="{{adminUrl}}" target="_blank" style="display: inline-block; color: #ffffff; background-color: #3498db; border: solid 1px #3498db; border-radius: 5px; box-sizing: border-box; cursor: pointer; text-decoration: none; font-size: 14px; font-weight: bold; margin: 0; padding: 12px 25px; text-transform: capitalize; border-color: #3498db;">View User in Admin</a>
                                                </td>
                                            </tr>
                                        </tbody>
                                    </table>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                    
                    <p style="font-family: sans-serif; font-size: 14px; font-weight: normal; margin: 0; margin-bottom: 15px;">This user has been automatically created and will need to verify their email address before they can fully access the platform.</p>
                </td>
            </tr>
        </table>
    </td>
</tr>

{{> footer}}
