#!/usr/bin/env node

/**
 * Comprehensive Edge Case Testing Script for AI System
 * Tests all potential crash scenarios and edge cases
 */

const axios = require('axios');

const BASE_URL = 'http://localhost:3000';
const TEST_USER_ID = 'test_user_123';

// Test configurations
const TESTS = {
    // AI Assistant Tests
    aiAssistant: [
        {
            name: 'Empty message',
            endpoint: '/ai-assistant/chat',
            data: { message: '', context: {} },
            expectStatus: 422
        },
        {
            name: 'Null message',
            endpoint: '/ai-assistant/chat',
            data: { message: null, context: {} },
            expectStatus: 422
        },
        {
            name: 'Very long message',
            endpoint: '/ai-assistant/chat',
            data: { message: 'a'.repeat(5000), context: {} },
            expectStatus: 422
        },
        {
            name: 'Invalid context type',
            endpoint: '/ai-assistant/chat',
            data: { message: 'test', context: 'invalid' },
            expectStatus: 200 // Should handle gracefully
        },
        {
            name: 'Malicious script in message',
            endpoint: '/ai-assistant/chat',
            data: { message: '<script>alert("xss")</script>', context: {} },
            expectStatus: 200 // Should sanitize
        },
        {
            name: 'SQL injection attempt',
            endpoint: '/ai-assistant/chat',
            data: { message: "'; DROP TABLE users; --", context: {} },
            expectStatus: 200 // Should handle safely
        }
    ],

    // Analytics Tests
    analytics: [
        {
            name: 'Invalid event type',
            endpoint: '/analytics/track',
            data: { eventType: null, eventData: {} },
            expectStatus: 422
        },
        {
            name: 'Very long event type',
            endpoint: '/analytics/track',
            data: { eventType: 'a'.repeat(200), eventData: {} },
            expectStatus: 422
        },
        {
            name: 'Circular reference in event data',
            endpoint: '/analytics/track',
            data: { eventType: 'test', eventData: createCircularObject() },
            expectStatus: 200 // Should handle gracefully
        },
        {
            name: 'Deeply nested object',
            endpoint: '/analytics/track',
            data: { eventType: 'test', eventData: createDeeplyNestedObject(10) },
            expectStatus: 200 // Should handle gracefully
        },
        {
            name: 'Invalid page path',
            endpoint: '/analytics/track/page-view',
            data: { pagePath: null },
            expectStatus: 422
        },
        {
            name: 'Very long page path',
            endpoint: '/analytics/track/page-view',
            data: { pagePath: 'a'.repeat(1000) },
            expectStatus: 422
        },
        {
            name: 'Invalid time on page',
            endpoint: '/analytics/track/page-view',
            data: { pagePath: '/test', timeOnPage: 'invalid' },
            expectStatus: 200 // Should handle gracefully
        },
        {
            name: 'Negative scroll depth',
            endpoint: '/analytics/track/page-view',
            data: { pagePath: '/test', scrollDepth: -50 },
            expectStatus: 200 // Should handle gracefully
        }
    ],

    // HubSpot Integration Tests
    hubspot: [
        {
            name: 'Invalid user data',
            endpoint: '/analytics/user/invalid_user/sync-hubspot',
            data: { triggerType: 'test' },
            expectStatus: [404, 500] // User not found or sync error
        },
        {
            name: 'Missing email in user data',
            endpoint: '/analytics/user/' + TEST_USER_ID + '/sync-hubspot',
            data: { triggerType: 'test' },
            expectStatus: [422, 500] // Validation error or sync error
        }
    ],

    // Persona Classification Tests
    personaClassification: [
        {
            name: 'Invalid user ID for persona',
            endpoint: '/analytics/user/invalid_user/persona-classification',
            data: {},
            expectStatus: [404, 500] // User not found or classification error
        },
        {
            name: 'Empty user ID for persona',
            endpoint: '/analytics/user//persona-classification',
            data: {},
            expectStatus: 404 // Invalid route
        }
    ]
};

// Helper functions
function createCircularObject() {
    const obj = { name: 'test' };
    obj.self = obj; // Circular reference
    return obj;
}

function createDeeplyNestedObject(depth) {
    let obj = { value: 'deep' };
    for (let i = 0; i < depth; i++) {
        obj = { nested: obj };
    }
    return obj;
}

// Test runner
async function runTests() {
    console.log('🧪 Starting Edge Case Testing...\n');
    
    let totalTests = 0;
    let passedTests = 0;
    let failedTests = 0;

    for (const [category, tests] of Object.entries(TESTS)) {
        console.log(`\n📋 Testing ${category.toUpperCase()}:`);
        
        for (const test of tests) {
            totalTests++;
            
            try {
                const response = await axios.post(BASE_URL + test.endpoint, test.data, {
                    timeout: 10000,
                    validateStatus: () => true // Don't throw on HTTP errors
                });
                
                const expectedStatuses = Array.isArray(test.expectStatus) ? 
                    test.expectStatus : [test.expectStatus];
                
                if (expectedStatuses.includes(response.status)) {
                    console.log(`  ✅ ${test.name} - Status: ${response.status}`);
                    passedTests++;
                } else {
                    console.log(`  ❌ ${test.name} - Expected: ${test.expectStatus}, Got: ${response.status}`);
                    console.log(`     Response: ${JSON.stringify(response.data).substring(0, 200)}`);
                    failedTests++;
                }
                
            } catch (error) {
                if (error.code === 'ECONNREFUSED') {
                    console.log(`  ⚠️  ${test.name} - Server not running`);
                } else if (error.code === 'ECONNABORTED') {
                    console.log(`  ⏱️  ${test.name} - Request timeout (expected for some tests)`);
                    passedTests++; // Timeout handling is working
                } else {
                    console.log(`  ❌ ${test.name} - Error: ${error.message}`);
                    failedTests++;
                }
            }
        }
    }
    
    console.log('\n📊 Test Results:');
    console.log(`Total Tests: ${totalTests}`);
    console.log(`Passed: ${passedTests}`);
    console.log(`Failed: ${failedTests}`);
    console.log(`Success Rate: ${((passedTests / totalTests) * 100).toFixed(1)}%`);
    
    if (failedTests === 0) {
        console.log('\n🎉 All edge case tests passed!');
    } else {
        console.log('\n⚠️  Some tests failed. Review the error handling.');
    }
}

// Additional security tests
async function runSecurityTests() {
    console.log('\n🔒 Running Security Tests...\n');
    
    const securityTests = [
        {
            name: 'XSS in message',
            data: { message: '<img src=x onerror=alert(1)>', context: {} }
        },
        {
            name: 'HTML injection',
            data: { message: '<h1>Injected HTML</h1>', context: {} }
        },
        {
            name: 'JavaScript protocol',
            data: { message: 'javascript:alert("xss")', context: {} }
        },
        {
            name: 'Event handler injection',
            data: { message: 'onclick=alert(1)', context: {} }
        },
        {
            name: 'Large payload attack',
            data: { 
                message: 'test',
                context: { 
                    maliciousData: 'x'.repeat(100000) // 100KB payload
                }
            }
        }
    ];
    
    for (const test of securityTests) {
        try {
            const response = await axios.post(BASE_URL + '/ai-assistant/chat', test.data, {
                timeout: 10000,
                validateStatus: () => true
            });
            
            // Check if response contains unsanitized input
            const responseText = JSON.stringify(response.data);
            const containsScript = responseText.includes('<script>') || 
                                 responseText.includes('javascript:') ||
                                 responseText.includes('onerror=');
            
            if (containsScript) {
                console.log(`  ❌ ${test.name} - Potential XSS vulnerability detected`);
            } else {
                console.log(`  ✅ ${test.name} - Input properly sanitized`);
            }
            
        } catch (error) {
            console.log(`  ⚠️  ${test.name} - ${error.message}`);
        }
    }
}

// Performance tests
async function runPerformanceTests() {
    console.log('\n⚡ Running Performance Tests...\n');
    
    const performanceTests = [
        {
            name: 'Concurrent requests',
            test: async () => {
                const promises = [];
                for (let i = 0; i < 10; i++) {
                    promises.push(
                        axios.post(BASE_URL + '/ai-assistant/chat', {
                            message: `Test message ${i}`,
                            context: {}
                        }, { timeout: 30000, validateStatus: () => true })
                    );
                }
                
                const start = Date.now();
                const results = await Promise.allSettled(promises);
                const duration = Date.now() - start;
                
                const successful = results.filter(r => r.status === 'fulfilled').length;
                console.log(`  📊 ${successful}/10 requests successful in ${duration}ms`);
                
                return successful >= 8; // At least 80% success rate
            }
        },
        {
            name: 'Memory usage with large objects',
            test: async () => {
                const largeObject = {
                    data: Array(1000).fill(0).map((_, i) => ({
                        id: i,
                        name: `Item ${i}`,
                        description: 'x'.repeat(100)
                    }))
                };
                
                const response = await axios.post(BASE_URL + '/analytics/track', {
                    eventType: 'large_object_test',
                    eventData: largeObject
                }, { timeout: 10000, validateStatus: () => true });
                
                return response.status < 500; // Should handle without crashing
            }
        }
    ];
    
    for (const test of performanceTests) {
        try {
            const result = await test.test();
            console.log(`  ${result ? '✅' : '❌'} ${test.name}`);
        } catch (error) {
            console.log(`  ❌ ${test.name} - ${error.message}`);
        }
    }
}

// Main execution
async function main() {
    console.log('🚀 AI System Edge Case Testing Suite');
    console.log('=====================================');
    
    try {
        await runTests();
        await runSecurityTests();
        await runPerformanceTests();
        
        console.log('\n✨ Testing complete!');
        
    } catch (error) {
        console.error('❌ Testing failed:', error.message);
        process.exit(1);
    }
}

// Run if called directly
if (require.main === module) {
    main();
}

module.exports = { runTests, runSecurityTests, runPerformanceTests };
