make all --print-data-base --no-builtin-variables --no-builtin-rules --question
# GNU Make 3.81
 
# Copyright (C) 2006  Free Software Foundation, Inc.
# This is free software; see the source for copying conditions.
# There is NO warranty; not even for MERCHANTABILITY or FITNESS FOR A
# PARTICULAR PURPOSE.

# This program built for i386-apple-darwin11.3.0
 

# Make data base, printed on Thu Aug  4 10:56:23 2022

# Variables

# automatic
<D = $(patsubst %/,%,$(dir $<))
# automatic
?F = $(notdir $?)
# automatic
?D = $(patsubst %/,%,$(dir $?))
# automatic
@D = $(patsubst %/,%,$(dir $@))
# makefile (from `Makefile', line 2)
DOCKER_IMAGE_TAG = latest
# automatic
@F = $(notdir $@)
# makefile
CURDIR := /Users/<USER>/Documents/wd-projects/canideal
# makefile
SHELL = /bin/sh
# environment
VSCODE_NLS_CONFIG = {"locale":"en-us","availableLanguages":{},"_languagePackSupport":true}
# environment
_ = /usr/bin/make
# makefile (from `Makefile', line 1)
MAKEFILE_LIST :=  Makefile
# environment
__CFBundleIdentifier = com.microsoft.VSCode
# environment
VSCODE_CWD = /
# environment
PATH = /Users/<USER>/.nvm/versions/node/v12.22.12/bin:/usr/local/bin:/usr/bin:/bin:/usr/sbin:/sbin:/Library/Apple/usr/bin
# environment
LSCOLORS = Gxfxcxdxbxegedabagacad
# makefile (from `Makefile', line 1)
DOCKER_IMAGE = visualdemand/canideal
# environment
NVM_BIN = /Users/<USER>/.nvm/versions/node/v12.22.12/bin
# environment
ELECTRON_RUN_AS_NODE = 1
# default
.FEATURES := target-specific order-only second-expansion else-if archives jobserver check-symlink
# environment
SSH_AUTH_SOCK = /private/tmp/com.apple.launchd.bR4s3i8D5e/Listeners
# automatic
%F = $(notdir $%)
# environment
PWD = /Users/<USER>/Documents/wd-projects/canideal
# environment
ORIGINAL_XDG_CURRENT_DESKTOP = undefined
# environment
VSCODE_AMD_ENTRYPOINT = vs/workbench/api/node/extensionHostProcess
# environment
HOME = /Users/<USER>
# default
MAKEFILEPATH = $(shell /usr/bin/xcode-select -print-path 2>/dev/null || echo /Developer)/Makefiles
# environment
VSCODE_CODE_CACHE_PATH = /Users/<USER>/Library/Application Support/Code/CachedData/3b889b090b5ad5793f524b5d1d39fda662b96a2a
# environment
LOGNAME = win
# environment
APPLICATION_INSIGHTS_NO_DIAGNOSTIC_CHANNEL = true
# environment
NVM_CD_FLAGS = -q
# environment
ZSH = /Users/<USER>/.oh-my-zsh
# environment
VSCODE_HANDLES_UNCAUGHT_ERRORS = true
# automatic
^D = $(patsubst %/,%,$(dir $^))
# environment
XPC_FLAGS = 0x0
# default
MAKE = $(MAKE_COMMAND)
# default
MAKECMDGOALS := all
# environment
SHLVL = 1
# default
MAKE_VERSION := 3.81
# environment
USER = win
# makefile
.DEFAULT_GOAL := gitHash
# environment
LESS = -R
# automatic
%D = $(patsubst %/,%,$(dir $%))
# default
MAKE_COMMAND := /Library/Developer/CommandLineTools/usr/bin/make
# default
.VARIABLES := 
# environment
TMPDIR = /var/folders/76/b7pnpykd32gg58dvmnmg4r5c0000gn/T/
# automatic
*F = $(notdir $*)
# environment
VSCODE_IPC_HOOK = /Users/<USER>/Library/Application Support/Code/1.69.2-main.sock
# environment
MallocNanoZone = 0
# makefile
MAKEFLAGS = Rrqp
# environment
MFLAGS = -Rrqp
# automatic
*D = $(patsubst %/,%,$(dir $*))
# environment
NVM_DIR = /Users/<USER>/.nvm
# environment
XPC_SERVICE_NAME = application.com.microsoft.VSCode.92504129.92504135
# automatic
+D = $(patsubst %/,%,$(dir $+))
# automatic
+F = $(notdir $+)
# environment
__CF_USER_TEXT_ENCODING = 0x1F5:0x0:0x0
# environment
COMMAND_MODE = unix2003
# default
MAKEFILES := 
# automatic
<F = $(notdir $<)
# environment
PAGER = less
# environment
LC_ALL = C
# makefile (from `Makefile', line 3)
GIT_HASH = production
# automatic
^F = $(notdir $^)
# default
SUFFIXES := 
# default
.INCLUDE_DIRS = /usr/local/include
# environment
MAKELEVEL := 0
# environment
LANG = C
# environment
VSCODE_PID = 17185
# variable set hash-table stats:
# Load=68/1024=7%, Rehash=0, Collisions=1/95=1%

# Pattern-specific Variable Values

# No pattern-specific variable values.

# Directories

# . (device 16777220, inode 61830474): 29 files, no impossibilities.

# 29 files, no impossibilities in 1 directories.

# Implicit Rules

# No implicit rules.

# Files

docker_backend:
#  Implicit rule search has not been done.
#  Modification time never checked.
#  File has not been updated.
#  commands to execute (from `Makefile', line 18):
	docker build -f devops/docker/images/backend/Dockerfile -t ${DOCKER_IMAGE}:${GIT_HASH} -t ${DOCKER_IMAGE}:${DOCKER_IMAGE_TAG} .
	

docker_app:
#  Implicit rule search has not been done.
#  Modification time never checked.
#  File has not been updated.
#  commands to execute (from `Makefile', line 21):
	docker build -f devops/docker/images/frontend/Dockerfile -t ${DOCKER_IMAGE}-static:${GIT_HASH} -t ${DOCKER_IMAGE}-static:${DOCKER_IMAGE_TAG} .
	

# Not a target:
all:
#  Command-line target.
#  Implicit rule search has been done.
#  File does not exist.
#  File has not been updated.
# variable set hash-table stats:
# Load=0/32=0%, Rehash=0, Collisions=0/0=0%

# Not a target:
.SUFFIXES:
#  Implicit rule search has not been done.
#  Modification time never checked.
#  File has not been updated.

# Not a target:
Makefile:
#  Implicit rule search has been done.
#  Last modified 2022-03-19 12:35:47
#  File has been updated.
#  Successfully updated.
# variable set hash-table stats:
# Load=0/32=0%, Rehash=0, Collisions=0/0=0%

docker_tests:
#  Implicit rule search has not been done.
#  Modification time never checked.
#  File has not been updated.
#  commands to execute (from `Makefile', line 15):
	docker build -f devops/docker/images/tests/Dockerfile -t ${DOCKER_IMAGE}-base:${GIT_HASH} -t ${DOCKER_IMAGE}-base:${DOCKER_IMAGE_TAG} .
	

production_tests: docker
#  Implicit rule search has not been done.
#  Modification time never checked.
#  File has not been updated.
#  commands to execute (from `Makefile', line 26):
	docker-compose -f devops/docker/testing.yaml -p canideal up -d
	docker-compose -f devops/docker/testing.yaml -p canideal ps
	docker-compose -f devops/docker/testing.yaml -p canideal run --rm tests npm run test:migrate
	docker-compose -f devops/docker/testing.yaml -p canideal run --rm tests npm run test:report
	

# Not a target:
.DEFAULT:
#  Implicit rule search has not been done.
#  Modification time never checked.
#  File has not been updated.

docker: docker_tests docker_backend docker_app
#  Implicit rule search has not been done.
#  Modification time never checked.
#  File has not been updated.

gitHash:
#  Implicit rule search has not been done.
#  Modification time never checked.
#  File has not been updated.
#  commands to execute (from `Makefile', line 6):
	$(eval GIT_HASH = $(shell git rev-parse HEAD))
	$(eval DOCKER_IMAGE_TAG = $(shell git rev-parse --abbrev-ref HEAD))
	$(eval WORKSPACE = $(shell pwd))
	

upload: docker docker_push
#  Implicit rule search has not been done.
#  Modification time never checked.
#  File has not been updated.

docker_push:
#  Implicit rule search has not been done.
#  Modification time never checked.
#  File has not been updated.
#  commands to execute (from `Makefile', line 32):
	docker push ${DOCKER_IMAGE}:${GIT_HASH}
	docker push ${DOCKER_IMAGE}-static:${GIT_HASH}
	

# files hash-table stats:
# Load=12/1024=1%, Rehash=0, Collisions=0/31=0%
# VPATH Search Paths

# No `vpath' search paths.

# No general (`VPATH' variable) search path.

# # of strings in strcache: 1
# # of strcache buffers: 1
# strcache size: total = 4096 / max = 4096 / min = 4096 / avg = 4096
# strcache free: total = 4087 / max = 4087 / min = 4087 / avg = 4087

# Finished Make data base on Thu Aug  4 10:56:23 2022

 
make: *** No rule to make target `all'.  Stop.

