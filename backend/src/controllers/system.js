'use strict';

const Router = require('koa-router');
const mongoose = require('mongoose');
const ObjectId = mongoose.Types.ObjectId;
const config = require('../services/config');
const queue = require('../services/queue');


module.exports = Router()
    .post('/webhooks/sparkpost', postSparkpost)
    .post('/webhooks/hubspot', postHubSpot)
    .get('/ping', getPing);

async function getPing(ctx) {
    ctx.body = 'ok';
}

async function postSparkpost(ctx) {
    let payload = ctx.request.body;
    if (!Array.isArray(payload)) {
        payload = [payload];
    }
    const emailLogMap = {};
    const emailBlastMap = {};
    payload.forEach(({msys}) => {
        if (!msys) {
            return;
        }
        Object.keys(msys)
            .map(eventType => Object.assign(msys[eventType], {eventType}))
            // eslint-disable-next-line camelcase
            .filter(({rcpt_meta}) => rcpt_meta && rcpt_meta.env === config.app.env)
            .forEach(event => {
                if (event.rcpt_meta.messageId && ObjectId.isValid(event.rcpt_meta.messageId)) {
                    if (!emailLogMap[event.rcpt_meta.messageId]) {
                        emailLogMap[event.rcpt_meta.messageId] = [];
                    }
                    emailLogMap[event.rcpt_meta.messageId].push(event);
                }
                else if (event.rcpt_meta.emailBlastKey && event.rcpt_meta.userKey) {
                    if (!emailBlastMap[event.rcpt_meta.emailBlastKey]) {
                        emailBlastMap[event.rcpt_meta.emailBlastKey] = {};
                    }
                    if (!emailBlastMap[event.rcpt_meta.emailBlastKey][event.rcpt_meta.userKey]) {
                        emailBlastMap[event.rcpt_meta.emailBlastKey][event.rcpt_meta.userKey] = {};
                    }

                    emailBlastMap[event.rcpt_meta.emailBlastKey][event.rcpt_meta.userKey][event.type] = new Date(Number(event.timestamp) * 1000);
                }

            });
    });

    if (Object.keys(emailLogMap).length + Object.keys(emailBlastMap).length) {
        await queue.emails.sparkpostWebhook({
            emailLogMap,
            emailBlastMap
        });
    }

    ctx.response.status = 204;
}

async function postHubSpot(ctx) {
    try {
        const payload = ctx.request.body;
        const headers = ctx.request.headers;

        // Log the webhook for debugging
        console.log('HubSpot webhook received:', {
            timestamp: new Date().toISOString(),
            headers: {
                'x-hubspot-signature': headers['x-hubspot-signature'],
                'x-hubspot-request-timestamp': headers['x-hubspot-request-timestamp'],
                'content-type': headers['content-type']
            },
            payloadType: Array.isArray(payload) ? 'array' : typeof payload,
            payloadLength: Array.isArray(payload) ? payload.length : 1
        });

        // Ensure payload is an array for consistent processing
        const events = Array.isArray(payload) ? payload : [payload];

        // Process each event
        for (const event of events) {
            await processHubSpotEvent(event, headers);
        }

        // Return 200 status to acknowledge receipt
        ctx.response.status = 200;
        ctx.body = { received: true, processed: events.length };

    } catch (error) {
        console.error('Error processing HubSpot webhook:', error);

        // Still return 200 to prevent HubSpot from retrying
        // Log the error for investigation
        ctx.response.status = 200;
        ctx.body = { received: true, error: 'Processing failed' };
    }
}

async function processHubSpotEvent(event, headers) {
    try {
        // Extract event type and object type
        const eventType = event.eventType || event.subscriptionType;
        const objectType = event.objectType;
        const objectId = event.objectId;

        console.log('Processing HubSpot event:', {
            eventType,
            objectType,
            objectId,
            timestamp: event.occurredAt || event.eventTime
        });

        // Handle different event types
        switch (eventType) {
            case 'contact.propertyChange':
                await handleContactPropertyChange(event);
                break;

            case 'contact.creation':
                await handleContactCreation(event);
                break;

            case 'contact.deletion':
                await handleContactDeletion(event);
                break;

            case 'company.propertyChange':
                await handleCompanyPropertyChange(event);
                break;

            case 'deal.propertyChange':
                await handleDealPropertyChange(event);
                break;

            case 'workflow.enrollment':
                await handleWorkflowEnrollment(event);
                break;

            case 'workflow.completion':
                await handleWorkflowCompletion(event);
                break;

            default:
                // Log unknown event types for future implementation
                console.log('Unknown HubSpot event type:', eventType, event);
                break;
        }

    } catch (error) {
        console.error('Error processing individual HubSpot event:', error, event);
    }
}

// Event handlers for different HubSpot event types
async function handleContactPropertyChange(event) {
    console.log('Contact property changed:', {
        contactId: event.objectId,
        propertyName: event.propertyName,
        propertyValue: event.propertyValue
    });

    // TODO: Implement contact property change logic
    // Example: Update local user data based on HubSpot changes
}

async function handleContactCreation(event) {
    console.log('Contact created in HubSpot:', {
        contactId: event.objectId,
        timestamp: event.occurredAt
    });

    // TODO: Implement contact creation logic
    // Example: Log contact creation for analytics
}

async function handleContactDeletion(event) {
    console.log('Contact deleted in HubSpot:', {
        contactId: event.objectId,
        timestamp: event.occurredAt
    });

    // TODO: Implement contact deletion logic
    // Example: Mark user as deleted in local system
}

async function handleCompanyPropertyChange(event) {
    console.log('Company property changed:', {
        companyId: event.objectId,
        propertyName: event.propertyName,
        propertyValue: event.propertyValue
    });

    // TODO: Implement company property change logic
}

async function handleDealPropertyChange(event) {
    console.log('Deal property changed:', {
        dealId: event.objectId,
        propertyName: event.propertyName,
        propertyValue: event.propertyValue
    });

    // TODO: Implement deal property change logic
}

async function handleWorkflowEnrollment(event) {
    console.log('Contact enrolled in workflow:', {
        contactId: event.objectId,
        workflowId: event.workflowId,
        timestamp: event.occurredAt
    });

    // TODO: Implement workflow enrollment logic
    // Example: Track workflow enrollment in analytics
}

async function handleWorkflowCompletion(event) {
    console.log('Contact completed workflow:', {
        contactId: event.objectId,
        workflowId: event.workflowId,
        timestamp: event.occurredAt
    });

    // TODO: Implement workflow completion logic
    // Example: Trigger follow-up actions in CanIDeal
}
