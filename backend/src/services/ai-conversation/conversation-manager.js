'use strict';

const { AppError } = require('../error');

// In-memory conversation storage (in production, use Redis or database)
const conversationStore = new Map();

// Conversation context structure
class ConversationContext {
    constructor(userId, userRole) {
        this.userId = userId;
        this.userRole = userRole;
        this.messages = [];
        this.context = {};
        this.preferences = {};
        this.createdAt = new Date();
        this.lastActivity = new Date();
        this.sessionId = generateSessionId();
    }

    addMessage(type, content, metadata = {}) {
        const message = {
            type, // 'user' | 'ai' | 'system'
            content,
            metadata,
            timestamp: new Date()
        };
        
        this.messages.push(message);
        this.lastActivity = new Date();
        
        // Keep only last 20 messages to prevent memory bloat
        if (this.messages.length > 20) {
            this.messages = this.messages.slice(-20);
        }
        
        return message;
    }

    updateContext(key, value) {
        this.context[key] = value;
        this.lastActivity = new Date();
    }

    setPreference(key, value) {
        this.preferences[key] = value;
        this.lastActivity = new Date();
    }

    getRecentMessages(count = 5) {
        return this.messages.slice(-count);
    }

    getConversationSummary() {
        const recentMessages = this.getRecentMessages(10);
        const userMessages = recentMessages.filter(m => m.type === 'user');
        const topics = extractTopics(userMessages);
        
        return {
            sessionId: this.sessionId,
            messageCount: this.messages.length,
            duration: Date.now() - this.createdAt.getTime(),
            topics,
            lastActivity: this.lastActivity,
            context: this.context,
            preferences: this.preferences
        };
    }
}

// Get or create conversation context for a user
function getConversationContext(userId, userRole) {
    const key = `${userId}_${userRole}`;
    
    if (!conversationStore.has(key)) {
        conversationStore.set(key, new ConversationContext(userId, userRole));
    }
    
    const context = conversationStore.get(key);
    context.lastActivity = new Date();
    
    return context;
}

// Add message to conversation
function addMessage(userId, userRole, type, content, metadata = {}) {
    const context = getConversationContext(userId, userRole);
    return context.addMessage(type, content, metadata);
}

// Update conversation context
function updateConversationContext(userId, userRole, contextUpdates) {
    const context = getConversationContext(userId, userRole);
    
    Object.entries(contextUpdates).forEach(([key, value]) => {
        context.updateContext(key, value);
    });
    
    return context;
}

// Set user preferences
function setUserPreferences(userId, userRole, preferences) {
    const context = getConversationContext(userId, userRole);
    
    Object.entries(preferences).forEach(([key, value]) => {
        context.setPreference(key, value);
    });
    
    return context;
}

// Get conversation history for AI context
function getConversationHistory(userId, userRole, messageCount = 5) {
    const context = getConversationContext(userId, userRole);
    const recentMessages = context.getRecentMessages(messageCount);
    
    return {
        messages: recentMessages,
        context: context.context,
        preferences: context.preferences,
        summary: context.getConversationSummary()
    };
}

// Build enhanced context for AI with conversation history
function buildEnhancedContext(baseContext, userId, userRole) {
    const conversationHistory = getConversationHistory(userId, userRole);
    
    return {
        ...baseContext,
        conversation: {
            recentMessages: conversationHistory.messages,
            context: conversationHistory.context,
            preferences: conversationHistory.preferences,
            sessionInfo: conversationHistory.summary
        }
    };
}

// Clean up old conversations (run periodically)
function cleanupOldConversations(maxAgeHours = 24) {
    const cutoffTime = Date.now() - (maxAgeHours * 60 * 60 * 1000);
    
    for (const [key, context] of conversationStore.entries()) {
        if (context.lastActivity.getTime() < cutoffTime) {
            conversationStore.delete(key);
        }
    }
    
    return conversationStore.size;
}

// Extract topics from user messages
function extractTopics(messages) {
    const topics = new Set();
    const keywords = [
        'password', 'profile', 'product', 'order', 'ticket', 'support',
        'settings', 'account', 'inventory', 'payment', 'shipping',
        'metrc', 'compliance', 'application', 'verification'
    ];
    
    messages.forEach(message => {
        const content = message.content.toLowerCase();
        keywords.forEach(keyword => {
            if (content.includes(keyword)) {
                topics.add(keyword);
            }
        });
    });
    
    return Array.from(topics);
}

// Generate unique session ID
function generateSessionId() {
    return `session_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
}

// Get conversation statistics
function getConversationStats() {
    const stats = {
        activeConversations: conversationStore.size,
        totalMessages: 0,
        averageMessagesPerConversation: 0,
        topTopics: {},
        userRoleDistribution: {}
    };
    
    const allTopics = [];
    
    for (const context of conversationStore.values()) {
        stats.totalMessages += context.messages.length;
        
        // Count user roles
        stats.userRoleDistribution[context.userRole] = 
            (stats.userRoleDistribution[context.userRole] || 0) + 1;
        
        // Collect topics
        const summary = context.getConversationSummary();
        allTopics.push(...summary.topics);
    }
    
    if (stats.activeConversations > 0) {
        stats.averageMessagesPerConversation = 
            Math.round(stats.totalMessages / stats.activeConversations);
    }
    
    // Count topic frequency
    allTopics.forEach(topic => {
        stats.topTopics[topic] = (stats.topTopics[topic] || 0) + 1;
    });
    
    return stats;
}

// Export conversation data for a user (for data export/privacy)
function exportUserConversationData(userId, userRole) {
    const key = `${userId}_${userRole}`;
    const context = conversationStore.get(key);
    
    if (!context) {
        return null;
    }
    
    return {
        userId,
        userRole,
        sessionId: context.sessionId,
        createdAt: context.createdAt,
        lastActivity: context.lastActivity,
        messageCount: context.messages.length,
        messages: context.messages.map(msg => ({
            type: msg.type,
            content: msg.content,
            timestamp: msg.timestamp
        })),
        context: context.context,
        preferences: context.preferences
    };
}

// Delete user conversation data (for privacy/GDPR)
function deleteUserConversationData(userId, userRole) {
    const key = `${userId}_${userRole}`;
    return conversationStore.delete(key);
}

module.exports = {
    getConversationContext,
    addMessage,
    updateConversationContext,
    setUserPreferences,
    getConversationHistory,
    buildEnhancedContext,
    cleanupOldConversations,
    getConversationStats,
    exportUserConversationData,
    deleteUserConversationData
};
