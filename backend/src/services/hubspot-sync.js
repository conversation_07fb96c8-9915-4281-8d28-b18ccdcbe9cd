'use strict';

const mongoose = require('mongoose');
const hubspotService = require('./hubspot');
const queue = require('./queue');
const { AppError } = require('./error');

// Queue job types for HubSpot sync
const HUBSPOT_SYNC_USER = 'hubspot:sync:user';
const HUBSPOT_SYNC_BATCH = 'hubspot:sync:batch';
const HUBSPOT_TRIGGER_WORKFLOW = 'hubspot:trigger:workflow';

// Initialize HubSpot sync jobs
function initHubSpotSync() {
    // Register queue job handlers
    queue.define(HUBSPOT_SYNC_USER, syncUserJob);
    queue.define(HUBSPOT_SYNC_BATCH, syncBatchJob);
    queue.define(HUBSPOT_TRIGGER_WORKFLOW, triggerWorkflowJob);
    
    console.log('HubSpot sync jobs initialized');
}

// Sync user to HubSpot (immediate)
async function syncUserToHubSpot(userId, triggerType = 'signup', immediate = false) {
    try {
        const userData = await buildUserDataForSync(userId);
        
        if (!userData) {
            throw new Error(`User not found: ${userId}`);
        }
        
        if (immediate) {
            // Sync immediately
            return await hubspotService.syncUserToHubSpot(userData, triggerType);
        } else {
            // Queue for background processing
            await queue.now(HUBSPOT_SYNC_USER, {
                userId,
                triggerType,
                userData,
                timestamp: new Date()
            });
            
            return { success: true, queued: true };
        }
    } catch (error) {
        console.error('Error syncing user to HubSpot:', error);
        throw error;
    }
}

// Build comprehensive user data for HubSpot sync
async function buildUserDataForSync(userId) {
    const { User, Vendor, Retailer, Application } = mongoose.models;
    
    try {
        // Get base user data
        const user = await User.findById(userId).lean();
        if (!user) {
            return null;
        }
        
        let userData = {
            userId: user._id.toString(),
            email: user.email,
            name: user.name,
            phone: user.phone,
            createdAt: user.createdAt,
            lastLogin: user.lastLogin,
            userRole: 'user'
        };
        
        // Check if user is a vendor
        const vendor = await Vendor.findOne({ _user: userId }).lean();
        if (vendor) {
            userData.userRole = 'vendor';
            userData.storeName = vendor.storeName;
            userData.businessName = vendor.businessName;
            userData.website = vendor.website;
            userData.state = vendor.state;
            userData.licenseNumber = vendor.licenseNumber;
            userData.isDomestic = vendor.isDomestic;
            userData.enabled = vendor.enabled;
            
            // Get vendor application data
            const vendorApp = await Application.findOne({ 
                _vendor: vendor._id,
                type: 'vendor'
            }).lean();
            
            if (vendorApp) {
                userData.applicationStatus = vendorApp.status;
                userData.businessType = vendorApp.businessType;
                userData.applicationSubmittedAt = vendorApp.createdAt;
                userData.applicationApprovedAt = vendorApp.approvedAt;
            }
            
            // Get vendor statistics
            userData.productCount = await getVendorProductCount(vendor._id);
            userData.orderCount = await getVendorOrderCount(vendor._id);
            userData.totalRevenue = await getVendorTotalRevenue(vendor._id);
        }
        
        // Check if user is a retailer
        const retailer = await Retailer.findOne({ _user: userId }).lean();
        if (retailer) {
            userData.userRole = 'retailer';
            userData.businessName = retailer.businessName;
            userData.website = retailer.website;
            userData.state = retailer.state;
            userData.licenseNumber = retailer.licenseNumber;
            userData.isDomestic = retailer.isDomestic;
            userData.enabled = retailer.enabled;
            userData.retailerBudget = retailer.retailerBudget;
            
            // Get retailer application data
            const retailerApp = await Application.findOne({ 
                _retailer: retailer._id,
                type: 'retailer'
            }).lean();
            
            if (retailerApp) {
                userData.applicationStatus = retailerApp.status;
                userData.businessType = retailerApp.businessType;
                userData.applicationSubmittedAt = retailerApp.createdAt;
                userData.applicationApprovedAt = retailerApp.approvedAt;
            }
            
            // Get retailer statistics
            userData.orderCount = await getRetailerOrderCount(retailer._id);
            userData.totalSpent = await getRetailerTotalSpent(retailer._id);
        }
        
        return userData;
        
    } catch (error) {
        console.error('Error building user data for sync:', error);
        throw error;
    }
}

// Get vendor product count
async function getVendorProductCount(vendorId) {
    try {
        const { Product } = mongoose.models;
        return await Product.countDocuments({ _vendor: vendorId, enabled: true });
    } catch (error) {
        console.error('Error getting vendor product count:', error);
        return 0;
    }
}

// Get vendor order count
async function getVendorOrderCount(vendorId) {
    try {
        const { Order } = mongoose.models;
        return await Order.countDocuments({ _vendor: vendorId });
    } catch (error) {
        console.error('Error getting vendor order count:', error);
        return 0;
    }
}

// Get vendor total revenue
async function getVendorTotalRevenue(vendorId) {
    try {
        const { Order } = mongoose.models;
        const result = await Order.aggregate([
            { $match: { _vendor: vendorId, status: 'completed' } },
            { $group: { _id: null, total: { $sum: '$total' } } }
        ]);
        return result.length > 0 ? result[0].total : 0;
    } catch (error) {
        console.error('Error getting vendor total revenue:', error);
        return 0;
    }
}

// Get retailer order count
async function getRetailerOrderCount(retailerId) {
    try {
        const { Order } = mongoose.models;
        return await Order.countDocuments({ _retailer: retailerId });
    } catch (error) {
        console.error('Error getting retailer order count:', error);
        return 0;
    }
}

// Get retailer total spent
async function getRetailerTotalSpent(retailerId) {
    try {
        const { Order } = mongoose.models;
        const result = await Order.aggregate([
            { $match: { _retailer: retailerId, status: 'completed' } },
            { $group: { _id: null, total: { $sum: '$total' } } }
        ]);
        return result.length > 0 ? result[0].total : 0;
    } catch (error) {
        console.error('Error getting retailer total spent:', error);
        return 0;
    }
}

// Sync multiple users in batch
async function syncUsersBatch(userIds, triggerType = 'batch_sync') {
    try {
        await queue.now(HUBSPOT_SYNC_BATCH, {
            userIds,
            triggerType,
            timestamp: new Date()
        });
        
        return { success: true, queued: true, count: userIds.length };
    } catch (error) {
        console.error('Error queuing batch sync:', error);
        throw error;
    }
}

// Trigger HubSpot workflow for user
async function triggerHubSpotWorkflow(userId, workflowType, immediate = false) {
    try {
        if (immediate) {
            const userData = await buildUserDataForSync(userId);
            if (userData) {
                return await hubspotService.syncUserToHubSpot(userData, workflowType);
            }
        } else {
            await queue.now(HUBSPOT_TRIGGER_WORKFLOW, {
                userId,
                workflowType,
                timestamp: new Date()
            });
        }
        
        return { success: true, queued: !immediate };
    } catch (error) {
        console.error('Error triggering HubSpot workflow:', error);
        throw error;
    }
}

// Queue job handlers
async function syncUserJob(job) {
    const { userId, triggerType, userData } = job.attrs.data;
    
    try {
        console.log(`Processing HubSpot sync for user ${userId}, trigger: ${triggerType}`);
        
        const result = await hubspotService.syncUserToHubSpot(userData, triggerType);
        
        if (result.success) {
            console.log(`Successfully synced user ${userId} to HubSpot`);
        } else {
            console.error(`Failed to sync user ${userId} to HubSpot:`, result.error);
            throw new Error(result.error);
        }
        
    } catch (error) {
        console.error(`HubSpot sync job failed for user ${userId}:`, error);
        throw error;
    }
}

async function syncBatchJob(job) {
    const { userIds, triggerType } = job.attrs.data;
    
    try {
        console.log(`Processing HubSpot batch sync for ${userIds.length} users`);
        
        const results = [];
        for (const userId of userIds) {
            try {
                const userData = await buildUserDataForSync(userId);
                if (userData) {
                    const result = await hubspotService.syncUserToHubSpot(userData, triggerType);
                    results.push({ userId, success: result.success, error: result.error });
                }
                
                // Add delay between requests to respect rate limits
                await new Promise(resolve => setTimeout(resolve, 100));
                
            } catch (error) {
                console.error(`Error syncing user ${userId} in batch:`, error);
                results.push({ userId, success: false, error: error.message });
            }
        }
        
        const successCount = results.filter(r => r.success).length;
        console.log(`Batch sync completed: ${successCount}/${userIds.length} successful`);
        
    } catch (error) {
        console.error('HubSpot batch sync job failed:', error);
        throw error;
    }
}

async function triggerWorkflowJob(job) {
    const { userId, workflowType } = job.attrs.data;
    
    try {
        console.log(`Processing HubSpot workflow trigger for user ${userId}, type: ${workflowType}`);
        
        const userData = await buildUserDataForSync(userId);
        if (userData) {
            const result = await hubspotService.syncUserToHubSpot(userData, workflowType);
            
            if (result.success) {
                console.log(`Successfully triggered workflow for user ${userId}`);
            } else {
                console.error(`Failed to trigger workflow for user ${userId}:`, result.error);
                throw new Error(result.error);
            }
        }
        
    } catch (error) {
        console.error(`HubSpot workflow trigger job failed for user ${userId}:`, error);
        throw error;
    }
}

module.exports = {
    initHubSpotSync,
    syncUserToHubSpot,
    syncUsersBatch,
    triggerHubSpotWorkflow,
    buildUserDataForSync,
    constants: {
        HUBSPOT_SYNC_USER,
        HUBSPOT_SYNC_BATCH,
        HUBSPOT_TRIGGER_WORKFLOW
    }
};
