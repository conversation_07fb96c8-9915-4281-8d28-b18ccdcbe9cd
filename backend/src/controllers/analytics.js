'use strict';

const Router = require('koa-router');
const { HttpError } = require('../services/error');
const userAnalytics = require('../services/user-analytics');
const enhancedTracking = require('../services/enhanced-tracking');
const hubspotSync = require('../services/hubspot-sync');
const { personaClassifier } = require('../services/persona-classifier');

// Analytics API routes
const analyticsRouter = Router()
    .prefix('/analytics')
    .post('/track', postTrackEvent)
    .post('/track/page-view', postTrackPageView)
    .post('/track/search', postTrackSearch)
    .post('/track/product-interaction', postTrackProductInteraction)
    .post('/track/business-action', postTrackBusinessAction)
    .post('/track/ai-assistant', postTrackAIAssistant)
    .post('/track/demographic', postTrackDemographic)
    .post('/track/business-size', postTrackBusinessSize)
    .post('/track/purchase-history', postTrackPurchaseHistory)
    .post('/track/browsing-pattern', postTrackBrowsingPattern)
    .post('/track/device-fingerprint', postTrackDeviceFingerprint)
    .post('/track/location', postTrackLocation)
    .post('/track/referral', postTrackReferral)
    .post('/track/engagement-milestone', postTrackEngagementMilestone)
    .post('/track/conversion-funnel', postTrackConversionFunnel)
    .get('/user/:userId/insights', getUserInsights)
    .get('/user/:userId/enhanced-insights', getEnhancedUserInsights)
    .get('/user/:userId/persona', getUserPersona)
    .get('/user/:userId/persona-classification', getUserPersonaClassification)
    .get('/user/:userId/persona-recommendations', getUserPersonaRecommendations)
    .get('/platform/stats', getPlatformStats)
    .get('/platform/persona-distribution', getPersonaDistribution)
    .get('/hubspot/sync-stats', getHubSpotSyncStats)
    .post('/user/:userId/sync-hubspot', postSyncUserToHubSpot);

module.exports = analyticsRouter;

// Track generic user event
async function postTrackEvent(ctx) {
    const { eventType, eventData = {} } = ctx.request.body || {};
    const userId = ctx.state.user?._id;

    if (!userId) {
        throw new HttpError(401, 'User authentication required');
    }

    // Validate event type
    if (!eventType || typeof eventType !== 'string') {
        throw new HttpError(422, 'Event type is required and must be a string');
    }

    if (eventType.length > 100) {
        throw new HttpError(422, 'Event type is too long (max 100 characters)');
    }

    // Validate event data
    if (eventData && typeof eventData !== 'object') {
        throw new HttpError(422, 'Event data must be an object');
    }

    try {
        const context = {
            userAgent: sanitizeHeader(ctx.request.headers['user-agent']),
            ipAddress: ctx.request.ip || 'unknown',
            sessionId: ctx.session?.id || generateSessionId(),
            referrer: sanitizeHeader(ctx.request.headers.referer)
        };

        // Sanitize event data to prevent injection
        const sanitizedEventData = sanitizeEventData(eventData);

        const result = await enhancedTracking.trackUserBehavior(userId, eventType, {
            ...sanitizedEventData,
            ...context
        });

        ctx.body = {
            data: result
        };
    } catch (error) {
        console.error('Error tracking event:', error);
        if (error instanceof HttpError) {
            throw error;
        }
        throw new HttpError(500, 'Failed to track event');
    }
}

// Helper function to sanitize headers
function sanitizeHeader(header) {
    if (!header || typeof header !== 'string') {
        return 'unknown';
    }

    return header
        .replace(/[<>]/g, '') // Remove HTML tags
        .replace(/javascript:/gi, '') // Remove javascript: protocol
        .trim()
        .substring(0, 500); // Limit length
}

// Helper function to sanitize event data
function sanitizeEventData(data) {
    if (!data || typeof data !== 'object') {
        return {};
    }

    const sanitized = {};
    const maxDepth = 3;

    function sanitizeValue(value, depth = 0) {
        if (depth > maxDepth) {
            return '[Object too deep]';
        }

        if (value === null || value === undefined) {
            return value;
        }

        if (typeof value === 'string') {
            return value
                .replace(/[<>]/g, '') // Remove HTML tags
                .replace(/javascript:/gi, '') // Remove javascript: protocol
                .trim()
                .substring(0, 1000); // Limit string length
        }

        if (typeof value === 'number') {
            return isFinite(value) ? value : 0;
        }

        if (typeof value === 'boolean') {
            return value;
        }

        if (Array.isArray(value)) {
            return value.slice(0, 100).map(item => sanitizeValue(item, depth + 1));
        }

        if (typeof value === 'object') {
            const sanitizedObj = {};
            let count = 0;
            for (const [key, val] of Object.entries(value)) {
                if (count >= 50) break; // Limit object properties
                const sanitizedKey = key.replace(/[<>]/g, '').trim().substring(0, 100);
                sanitizedObj[sanitizedKey] = sanitizeValue(val, depth + 1);
                count++;
            }
            return sanitizedObj;
        }

        return String(value).substring(0, 100);
    }

    return sanitizeValue(data);
}

// Helper function to generate session ID
function generateSessionId() {
    return `session_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
}

// Track page view
async function postTrackPageView(ctx) {
    const { pagePath, timeOnPage, scrollDepth } = ctx.request.body || {};
    const userId = ctx.state.user?._id;

    if (!userId) {
        throw new HttpError(401, 'User authentication required');
    }

    // Validate page path
    if (!pagePath || typeof pagePath !== 'string') {
        throw new HttpError(422, 'Page path is required and must be a string');
    }

    if (pagePath.length > 500) {
        throw new HttpError(422, 'Page path is too long (max 500 characters)');
    }

    // Sanitize page path
    const sanitizedPagePath = pagePath
        .replace(/[<>]/g, '') // Remove HTML tags
        .replace(/javascript:/gi, '') // Remove javascript: protocol
        .trim();

    if (sanitizedPagePath.length === 0) {
        throw new HttpError(422, 'Page path cannot be empty');
    }

    // Validate numeric values
    let validTimeOnPage = 0;
    let validScrollDepth = 0;

    if (timeOnPage !== undefined) {
        const parsedTime = parseInt(timeOnPage);
        if (!isNaN(parsedTime) && parsedTime >= 0 && parsedTime <= 86400000) { // Max 24 hours
            validTimeOnPage = parsedTime;
        }
    }

    if (scrollDepth !== undefined) {
        const parsedDepth = parseInt(scrollDepth);
        if (!isNaN(parsedDepth) && parsedDepth >= 0 && parsedDepth <= 100) {
            validScrollDepth = parsedDepth;
        }
    }

    try {
        const context = {
            userAgent: sanitizeHeader(ctx.request.headers['user-agent']),
            ipAddress: ctx.request.ip || 'unknown',
            sessionId: ctx.session?.id || generateSessionId(),
            referrer: sanitizeHeader(ctx.request.headers.referer),
            timeOnPage: validTimeOnPage,
            scrollDepth: validScrollDepth
        };

        const result = await enhancedTracking.trackPageView(userId, sanitizedPagePath, context);

        ctx.body = {
            data: result
        };
    } catch (error) {
        console.error('Error tracking page view:', error);
        if (error instanceof HttpError) {
            throw error;
        }
        throw new HttpError(500, 'Failed to track page view');
    }
}

// Track search
async function postTrackSearch(ctx) {
    const { searchQuery, results = [], filters = {}, sortBy } = ctx.request.body;
    const userId = ctx.state.user?._id;
    
    if (!userId) {
        throw new HttpError(401, 'User authentication required');
    }
    
    if (!searchQuery) {
        throw new HttpError(422, 'Search query is required');
    }
    
    try {
        const context = {
            userAgent: ctx.request.headers['user-agent'],
            ipAddress: ctx.request.ip,
            sessionId: ctx.session.id || 'unknown',
            filters,
            sortBy
        };
        
        const result = await enhancedTracking.trackSearch(userId, searchQuery, results, context);
        
        ctx.body = {
            data: result
        };
    } catch (error) {
        console.error('Error tracking search:', error);
        throw new HttpError(500, 'Failed to track search');
    }
}

// Track product interaction
async function postTrackProductInteraction(ctx) {
    const { productId, interactionType, productCategory, vendorId, price } = ctx.request.body;
    const userId = ctx.state.user?._id;
    
    if (!userId) {
        throw new HttpError(401, 'User authentication required');
    }
    
    if (!productId || !interactionType) {
        throw new HttpError(422, 'Product ID and interaction type are required');
    }
    
    try {
        const context = {
            userAgent: ctx.request.headers['user-agent'],
            ipAddress: ctx.request.ip,
            sessionId: ctx.session.id || 'unknown',
            productCategory,
            vendorId,
            price
        };
        
        const result = await enhancedTracking.trackProductInteraction(userId, productId, interactionType, context);
        
        ctx.body = {
            data: result
        };
    } catch (error) {
        console.error('Error tracking product interaction:', error);
        throw new HttpError(500, 'Failed to track product interaction');
    }
}

// Track business action
async function postTrackBusinessAction(ctx) {
    const { actionType, actionData = {} } = ctx.request.body;
    const userId = ctx.state.user?._id;
    
    if (!userId) {
        throw new HttpError(401, 'User authentication required');
    }
    
    if (!actionType) {
        throw new HttpError(422, 'Action type is required');
    }
    
    try {
        const context = {
            userAgent: ctx.request.headers['user-agent'],
            ipAddress: ctx.request.ip,
            sessionId: ctx.session.id || 'unknown'
        };
        
        const result = await enhancedTracking.trackBusinessAction(userId, actionType, actionData, context);
        
        ctx.body = {
            data: result
        };
    } catch (error) {
        console.error('Error tracking business action:', error);
        throw new HttpError(500, 'Failed to track business action');
    }
}

// Track AI assistant usage
async function postTrackAIAssistant(ctx) {
    const { interactionType, messageCount, actionExecuted, satisfactionRating } = ctx.request.body;
    const userId = ctx.state.user?._id;
    
    if (!userId) {
        throw new HttpError(401, 'User authentication required');
    }
    
    if (!interactionType) {
        throw new HttpError(422, 'Interaction type is required');
    }
    
    try {
        const context = {
            userAgent: ctx.request.headers['user-agent'],
            ipAddress: ctx.request.ip,
            sessionId: ctx.session.id || 'unknown',
            messageCount,
            actionExecuted,
            satisfactionRating
        };
        
        const result = await enhancedTracking.trackAIAssistantUsage(userId, interactionType, context);
        
        ctx.body = {
            data: result
        };
    } catch (error) {
        console.error('Error tracking AI assistant usage:', error);
        throw new HttpError(500, 'Failed to track AI assistant usage');
    }
}

// Track demographic information
async function postTrackDemographic(ctx) {
    const { demographicData = {} } = ctx.request.body;
    const userId = ctx.state.user?._id;

    if (!userId) {
        throw new HttpError(401, 'User authentication required');
    }

    try {
        const context = {
            userAgent: ctx.request.headers['user-agent'],
            ipAddress: ctx.request.ip,
            sessionId: ctx.session.id || 'unknown'
        };

        const result = await enhancedTracking.trackDemographicUpdate(userId, demographicData, context);

        ctx.body = {
            data: result
        };
    } catch (error) {
        console.error('Error tracking demographic data:', error);
        throw new HttpError(500, 'Failed to track demographic data');
    }
}

// Track business size detection
async function postTrackBusinessSize(ctx) {
    const { detectedSize, indicators = {} } = ctx.request.body;
    const userId = ctx.state.user?._id;

    if (!userId) {
        throw new HttpError(401, 'User authentication required');
    }

    if (!detectedSize) {
        throw new HttpError(422, 'Detected size is required');
    }

    try {
        const context = {
            userAgent: ctx.request.headers['user-agent'],
            ipAddress: ctx.request.ip,
            sessionId: ctx.session.id || 'unknown'
        };

        const result = await enhancedTracking.trackBusinessSizeDetection(userId, detectedSize, indicators, context);

        ctx.body = {
            data: result
        };
    } catch (error) {
        console.error('Error tracking business size:', error);
        throw new HttpError(500, 'Failed to track business size');
    }
}

// Track purchase history analysis
async function postTrackPurchaseHistory(ctx) {
    const { analysisResults = {} } = ctx.request.body;
    const userId = ctx.state.user?._id;

    if (!userId) {
        throw new HttpError(401, 'User authentication required');
    }

    try {
        const context = {
            userAgent: ctx.request.headers['user-agent'],
            ipAddress: ctx.request.ip,
            sessionId: ctx.session.id || 'unknown'
        };

        const result = await enhancedTracking.trackPurchaseHistoryAnalysis(userId, analysisResults, context);

        ctx.body = {
            data: result
        };
    } catch (error) {
        console.error('Error tracking purchase history:', error);
        throw new HttpError(500, 'Failed to track purchase history');
    }
}

// Track browsing patterns
async function postTrackBrowsingPattern(ctx) {
    const { patternType, patternData = {} } = ctx.request.body;
    const userId = ctx.state.user?._id;

    if (!userId) {
        throw new HttpError(401, 'User authentication required');
    }

    if (!patternType) {
        throw new HttpError(422, 'Pattern type is required');
    }

    try {
        const context = {
            userAgent: ctx.request.headers['user-agent'],
            ipAddress: ctx.request.ip,
            sessionId: ctx.session.id || 'unknown'
        };

        const result = await enhancedTracking.trackBrowsingPattern(userId, patternType, patternData, context);

        ctx.body = {
            data: result
        };
    } catch (error) {
        console.error('Error tracking browsing pattern:', error);
        throw new HttpError(500, 'Failed to track browsing pattern');
    }
}

// Track device fingerprint
async function postTrackDeviceFingerprint(ctx) {
    const { deviceData = {} } = ctx.request.body;
    const userId = ctx.state.user?._id;

    if (!userId) {
        throw new HttpError(401, 'User authentication required');
    }

    try {
        const context = {
            userAgent: ctx.request.headers['user-agent'],
            ipAddress: ctx.request.ip,
            sessionId: ctx.session.id || 'unknown'
        };

        const result = await enhancedTracking.trackDeviceFingerprint(userId, deviceData, context);

        ctx.body = {
            data: result
        };
    } catch (error) {
        console.error('Error tracking device fingerprint:', error);
        throw new HttpError(500, 'Failed to track device fingerprint');
    }
}

// Track location data
async function postTrackLocation(ctx) {
    const { locationData = {} } = ctx.request.body;
    const userId = ctx.state.user?._id;

    if (!userId) {
        throw new HttpError(401, 'User authentication required');
    }

    try {
        const context = {
            userAgent: ctx.request.headers['user-agent'],
            ipAddress: ctx.request.ip,
            sessionId: ctx.session.id || 'unknown'
        };

        const result = await enhancedTracking.trackLocationData(userId, locationData, context);

        ctx.body = {
            data: result
        };
    } catch (error) {
        console.error('Error tracking location data:', error);
        throw new HttpError(500, 'Failed to track location data');
    }
}

// Track referral source
async function postTrackReferral(ctx) {
    const { referralData = {} } = ctx.request.body;
    const userId = ctx.state.user?._id;

    if (!userId) {
        throw new HttpError(401, 'User authentication required');
    }

    try {
        const context = {
            userAgent: ctx.request.headers['user-agent'],
            ipAddress: ctx.request.ip,
            sessionId: ctx.session.id || 'unknown'
        };

        const result = await enhancedTracking.trackReferralSource(userId, referralData, context);

        ctx.body = {
            data: result
        };
    } catch (error) {
        console.error('Error tracking referral source:', error);
        throw new HttpError(500, 'Failed to track referral source');
    }
}

// Track engagement milestone
async function postTrackEngagementMilestone(ctx) {
    const { milestone, milestoneData = {} } = ctx.request.body;
    const userId = ctx.state.user?._id;

    if (!userId) {
        throw new HttpError(401, 'User authentication required');
    }

    if (!milestone) {
        throw new HttpError(422, 'Milestone is required');
    }

    try {
        const context = {
            userAgent: ctx.request.headers['user-agent'],
            ipAddress: ctx.request.ip,
            sessionId: ctx.session.id || 'unknown'
        };

        const result = await enhancedTracking.trackEngagementMilestone(userId, milestone, milestoneData, context);

        ctx.body = {
            data: result
        };
    } catch (error) {
        console.error('Error tracking engagement milestone:', error);
        throw new HttpError(500, 'Failed to track engagement milestone');
    }
}

// Track conversion funnel step
async function postTrackConversionFunnel(ctx) {
    const { funnelStep, stepData = {} } = ctx.request.body;
    const userId = ctx.state.user?._id;

    if (!userId) {
        throw new HttpError(401, 'User authentication required');
    }

    if (!funnelStep) {
        throw new HttpError(422, 'Funnel step is required');
    }

    try {
        const context = {
            userAgent: ctx.request.headers['user-agent'],
            ipAddress: ctx.request.ip,
            sessionId: ctx.session.id || 'unknown'
        };

        const result = await enhancedTracking.trackConversionFunnelStep(userId, funnelStep, stepData, context);

        ctx.body = {
            data: result
        };
    } catch (error) {
        console.error('Error tracking conversion funnel step:', error);
        throw new HttpError(500, 'Failed to track conversion funnel step');
    }
}

// Get enhanced user behavior insights
async function getEnhancedUserInsights(ctx) {
    const { userId } = ctx.params;
    const requestingUserId = ctx.state.user?._id;

    // Users can only view their own insights, admins can view any
    if (userId !== requestingUserId.toString() && !isAdmin(ctx)) {
        throw new HttpError(403, 'Access denied');
    }

    try {
        const insights = await enhancedTracking.getEnhancedUserInsights(userId);

        ctx.body = {
            data: insights
        };
    } catch (error) {
        console.error('Error getting enhanced user insights:', error);
        throw new HttpError(500, 'Failed to get enhanced user insights');
    }
}

// Get user behavior insights
async function getUserInsights(ctx) {
    const { userId } = ctx.params;
    const requestingUserId = ctx.state.user?._id;
    
    // Users can only view their own insights, admins can view any
    if (userId !== requestingUserId.toString() && !isAdmin(ctx)) {
        throw new HttpError(403, 'Access denied');
    }
    
    try {
        const insights = await enhancedTracking.getUserBehaviorInsights(userId);
        
        ctx.body = {
            data: insights
        };
    } catch (error) {
        console.error('Error getting user insights:', error);
        throw new HttpError(500, 'Failed to get user insights');
    }
}

// Get user persona
async function getUserPersona(ctx) {
    const { userId } = ctx.params;
    const requestingUserId = ctx.state.user?._id;

    // Users can only view their own persona, admins can view any
    if (userId !== requestingUserId.toString() && !isAdmin(ctx)) {
        throw new HttpError(403, 'Access denied');
    }

    try {
        const persona = await userAnalytics.analyzeUserPersona(userId);

        ctx.body = {
            data: persona
        };
    } catch (error) {
        console.error('Error getting user persona:', error);
        throw new HttpError(500, 'Failed to get user persona');
    }
}

// Get detailed persona classification
async function getUserPersonaClassification(ctx) {
    const { userId } = ctx.params;
    const requestingUserId = ctx.state.user?._id;

    // Users can only view their own classification, admins can view any
    if (userId !== requestingUserId.toString() && !isAdmin(ctx)) {
        throw new HttpError(403, 'Access denied');
    }

    try {
        const classification = await personaClassifier.classifyUserPersona(userId);

        ctx.body = {
            data: classification
        };
    } catch (error) {
        console.error('Error getting persona classification:', error);
        throw new HttpError(500, 'Failed to get persona classification');
    }
}

// Get persona-based marketing recommendations
async function getUserPersonaRecommendations(ctx) {
    const { userId } = ctx.params;
    const requestingUserId = ctx.state.user?._id;

    // Users can only view their own recommendations, admins can view any
    if (userId !== requestingUserId.toString() && !isAdmin(ctx)) {
        throw new HttpError(403, 'Access denied');
    }

    try {
        const classification = await personaClassifier.classifyUserPersona(userId);
        const recommendations = personaClassifier.getPersonaRecommendations(classification);

        ctx.body = {
            data: {
                classification: classification.primaryPersona,
                recommendations
            }
        };
    } catch (error) {
        console.error('Error getting persona recommendations:', error);
        throw new HttpError(500, 'Failed to get persona recommendations');
    }
}

// Get platform statistics (admin only)
async function getPlatformStats(ctx) {
    if (!isAdmin(ctx)) {
        throw new HttpError(403, 'Admin access required');
    }

    try {
        const stats = enhancedTracking.getPlatformAnalytics();

        ctx.body = {
            data: stats
        };
    } catch (error) {
        console.error('Error getting platform stats:', error);
        throw new HttpError(500, 'Failed to get platform statistics');
    }
}

// Get persona distribution across platform (admin only)
async function getPersonaDistribution(ctx) {
    if (!isAdmin(ctx)) {
        throw new HttpError(403, 'Admin access required');
    }

    try {
        // This would typically query the database for all users
        // For now, return a sample distribution
        const distribution = {
            totalUsers: 1250,
            vendorPersonas: {
                'ENTERPRISE_VENDOR': 45,
                'GROWTH_VENDOR': 120,
                'BOUTIQUE_VENDOR': 180,
                'STARTUP_VENDOR': 255
            },
            retailerPersonas: {
                'ENTERPRISE_RETAILER': 25,
                'REGIONAL_RETAILER': 85,
                'INDEPENDENT_RETAILER': 320,
                'EMERGING_RETAILER': 220
            },
            lastUpdated: new Date(),
            dataQuality: {
                high: 450,
                medium: 600,
                low: 200
            }
        };

        ctx.body = {
            data: distribution
        };
    } catch (error) {
        console.error('Error getting persona distribution:', error);
        throw new HttpError(500, 'Failed to get persona distribution');
    }
}

// Get HubSpot sync statistics (admin only)
async function getHubSpotSyncStats(ctx) {
    if (!isAdmin(ctx)) {
        throw new HttpError(403, 'Admin access required');
    }
    
    try {
        const stats = await hubspotSync.getSyncStats();
        
        ctx.body = {
            data: stats
        };
    } catch (error) {
        console.error('Error getting HubSpot sync stats:', error);
        ctx.body = {
            data: { error: 'Failed to get sync statistics' }
        };
    }
}

// Manually sync user to HubSpot (admin only)
async function postSyncUserToHubSpot(ctx) {
    const { userId } = ctx.params;
    const { triggerType = 'manual_sync', immediate = true } = ctx.request.body;
    
    if (!isAdmin(ctx)) {
        throw new HttpError(403, 'Admin access required');
    }
    
    try {
        const result = await hubspotSync.syncUserToHubSpot(userId, triggerType, immediate);
        
        ctx.body = {
            data: result
        };
    } catch (error) {
        console.error('Error syncing user to HubSpot:', error);
        throw new HttpError(500, 'Failed to sync user to HubSpot');
    }
}

// Helper function to check if user is admin
function isAdmin(ctx) {
    return ctx.state.user && ctx.state.user.role === 'admin';
}

// Middleware to track API usage
async function trackAPIUsage(ctx, next) {
    const startTime = Date.now();
    
    await next();
    
    const endTime = Date.now();
    const duration = endTime - startTime;
    
    // Track API usage for analytics
    if (ctx.state.user) {
        try {
            await enhancedTracking.trackUserBehavior(ctx.state.user._id, 'api_usage', {
                endpoint: ctx.path,
                method: ctx.method,
                duration,
                statusCode: ctx.status,
                userAgent: ctx.request.headers['user-agent']
            });
        } catch (error) {
            console.error('Error tracking API usage:', error);
        }
    }
}

// Apply tracking middleware to all routes
analyticsRouter.use(trackAPIUsage);
