"use strict";

const config = require("../../services/config");
const mongoose = require("mongoose");
const ObjectId = mongoose.Types.ObjectId;
const Bluebird = require("bluebird");
const { HttpError } = require("../../services/error");
const escapeRegex = require("escape-string-regexp");
const constants = require("../../models/constants");
const { eqVariants } = require("../../services/util");
const { calcMeta } = require("./middleware");
const mw = require("../../services/middleware");
const Router = require("koa-router");
const { retailerSpa } = require("./middleware");
const campaignTriggers = require("../../services/campaign-triggers");

const retailerRouter = Router()
  .prefix('/retailer')
  .use(retailerSpa)
  .get('/application', getApplication)
  .post('/application', postApplication)
  .put('/application', putApplication)
  .post('/me', postMe)
  .post('/switch', postSwitchAccount)
  .patch('/terms-and-conditions', patchTermsAndConditions)
  .use(require('./store').routes())
  .use(require('./order').routes())
  .use(require('./product').routes())
  .use(require('./cart').routes())
  .use(require('./settings').routes())
  .use(require('./ticket').routes())
  .use(require('./coupon').routes())
  .get('*', () => ({}));


module.exports = retailerRouter;

async function getApplication(ctx) {

  const { Application } = mongoose.models;
  const { retailer } = ctx.state;
  const application = await Application.getForRetailer(retailer);

  if (application) {
    ctx.body = {
      data: {
        ok: true,
        application: { ...application._doc, ein: application.ein },
      }
    };
  } else {
    ctx.body = {
      data: {
        ok: false,
        message: "No Application Found"
      }
    };
  }
}

async function putApplication(ctx) {
  const { SystemLog } = mongoose.models;
  const { Application } = mongoose.models;
  const { retailer } = ctx.state;
  const { businessName, tradeName, ein, legalAddress, fullName, phone, license, files,
    description, monthlySales, proofOfAddress, personalId,
    agreeTermsOfServiceAndPrivacyPolicy, agreeUserFeesAndPaymentPolicy, agreeReturnPolicy, forDraft } = ctx.request.body;

  try {
    const updatedApplication = await Application.updateForRetailer({
      businessName, tradeName, ein, legalAddress, fullName, phone, license, files,
      description, monthlySales, proofOfAddress, personalId,
      agreeTermsOfServiceAndPrivacyPolicy, agreeUserFeesAndPaymentPolicy, agreeReturnPolicy, retailer, forDraft
    });

    await SystemLog.retailerApplicationUpdated(ctx, updatedApplication);

    ctx.body = {
      data: {
        application: updatedApplication.formatPublic()
      }
    };
  } catch (err) {
    console.log('there was an error updating the application.', err);
    // Properly handle and return the error to the client
    if (err.code === 11000) {
      throw new HttpError(422, 'Duplicate key error', { error: 'A record with this information already exists' });
    } else if (err.name === 'ValidationError') {
      throw new HttpError(422, 'Validation error', err.errors);
    } else {
      throw new HttpError(500, 'Error updating application', { error: err.message });
    }
  }
}

async function postApplication(ctx) {
  const { SystemLog } = mongoose.models;
  const { Application } = mongoose.models;
  const { businessName, tradeName, ein, legalAddress, fullName, phone, license, files,
    description, monthlySales, proofOfAddress, personalId,
    agreeTermsOfServiceAndPrivacyPolicy, agreeUserFeesAndPaymentPolicy, agreeReturnPolicy, isDraft } = ctx.request.body;
  const { retailer } = ctx.state;

  // if (!constants.einRegex.test(ein)) {
  //     throw new HttpError(422, 'Invalid ein', {ein: 'invalid number'});
  // }

  try {
    const application = await Application.createForRetailer({
      retailer,
      description, monthlySales, proofOfAddress, personalId,
      businessName, tradeName, ein, legalAddress, fullName, phone, license, files,
      agreeTermsOfServiceAndPrivacyPolicy, agreeUserFeesAndPaymentPolicy, agreeReturnPolicy, isDraft
    });

    SystemLog.retailerApplicationCreated(ctx, application);

    // Trigger HubSpot campaign for retailer application submission
    try {
      await campaignTriggers.triggerCampaign(campaignTriggers.TRIGGER_EVENTS.APPLICATION_SUBMITTED, {
        userId: ctx.state.user._id,
        applicationType: 'retailer',
        applicationId: application._id,
        businessName,
        isDraft
      });
    } catch (error) {
      console.error('Error triggering retailer application submitted campaign:', error);
      // Don't fail the main request if campaign trigger fails
    }

    ctx.body = {
      data: {
        application: application.formatPublic()
      }
    };
  }
  catch (err) {
    console.log('there was an error creating the application.', err);
    // Properly handle and return the error to the client
    if (err.code === 11000) {
      throw new HttpError(422, 'Duplicate key error', { error: 'A record with this information already exists' });
    } else if (err.name === 'ValidationError') {
      throw new HttpError(422, 'Validation error', err.errors);
    } else {
      throw new HttpError(500, 'Error creating application', { error: err.message });
    }
  }
}

async function postMe(ctx) {
  const cart = await ctx.state.cart.checkAndFormatPublic();
  await ctx.state.user.getRetailersAndVendors();

  ctx.body = {
    data: {
      cart,
      fakeUser: false,
      meta: await calcMeta(ctx),
      retailer: ctx.state.retailer.formatSource(),
      user: ctx.state.user.formatSource(),
    },
  };
}

async function postSwitchAccount(ctx) {
  const { user } = ctx.state;
  const { key } = ctx.request.body;

  const role = await user.switchRetailerAndVendorAccounts(key);

  ctx.body = {
    data: "/" + role,
  };
}

async function patchTermsAndConditions(ctx) {
  const { SystemLog } = mongoose.models;
  const { retailer } = ctx.state;

  retailer.set({ lastAgreeTermsAt: new Date() });
  await retailer.save();
  await SystemLog.retailerTermsAgreed(ctx, retailer);

  ctx.body = {
    data: retailer.formatSource(),
  };
}
