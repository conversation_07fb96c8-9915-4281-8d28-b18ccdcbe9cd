'use strict';

const userAnalytics = require('./user-analytics');
const enhancedTracking = require('./enhanced-tracking');

// Detailed buyer persona definitions
const BUYER_PERSONAS = {
    // Vendor Personas
    ENTERPRISE_VENDOR: {
        name: 'Enterprise Vendor',
        description: 'Large-scale cannabis businesses with high volume and established operations',
        characteristics: ['high_volume', 'established', 'multiple_locations', 'professional_operations'],
        thresholds: {
            productCount: 100,
            monthlyRevenue: 500000,
            employeeCount: 50,
            yearsInBusiness: 5
        }
    },
    GROWTH_VENDOR: {
        name: 'Growth Vendor',
        description: 'Mid-sized vendors scaling their operations and expanding market reach',
        characteristics: ['scaling', 'expanding', 'moderate_volume', 'growth_focused'],
        thresholds: {
            productCount: 25,
            monthlyRevenue: 100000,
            employeeCount: 10,
            yearsInBusiness: 2
        }
    },
    BOUTIQUE_VENDOR: {
        name: 'Boutique Vendor',
        description: 'Small, specialized vendors focusing on premium or niche products',
        characteristics: ['premium_focus', 'specialized', 'quality_over_quantity', 'artisanal'],
        thresholds: {
            productCount: 10,
            monthlyRevenue: 25000,
            averageProductPrice: 100,
            premiumBrand: true
        }
    },
    STARTUP_VENDOR: {
        name: 'Startup Vendor',
        description: 'New vendors just entering the market with limited operations',
        characteristics: ['new_to_market', 'learning', 'limited_resources', 'high_potential'],
        thresholds: {
            productCount: 5,
            monthlyRevenue: 5000,
            accountAge: 90, // days
            applicationStatus: 'pending'
        }
    },

    // Retailer Personas
    ENTERPRISE_RETAILER: {
        name: 'Enterprise Retailer',
        description: 'Large retail chains with multiple locations and high purchase volumes',
        characteristics: ['multi_location', 'high_volume', 'bulk_purchasing', 'established_operations'],
        thresholds: {
            monthlyOrders: 50,
            monthlySpend: 100000,
            locationCount: 5,
            yearsInBusiness: 3
        }
    },
    REGIONAL_RETAILER: {
        name: 'Regional Retailer',
        description: 'Mid-sized retailers serving regional markets with steady growth',
        characteristics: ['regional_focus', 'steady_growth', 'moderate_volume', 'market_expansion'],
        thresholds: {
            monthlyOrders: 20,
            monthlySpend: 25000,
            locationCount: 2,
            yearsInBusiness: 1
        }
    },
    INDEPENDENT_RETAILER: {
        name: 'Independent Retailer',
        description: 'Single-location retailers focusing on local community and customer service',
        characteristics: ['local_focus', 'community_oriented', 'personalized_service', 'curated_selection'],
        thresholds: {
            monthlyOrders: 10,
            monthlySpend: 10000,
            locationCount: 1,
            customerServiceFocus: true
        }
    },
    EMERGING_RETAILER: {
        name: 'Emerging Retailer',
        description: 'New retailers establishing their business and building supplier relationships',
        characteristics: ['new_business', 'relationship_building', 'market_entry', 'learning_focused'],
        thresholds: {
            monthlyOrders: 3,
            monthlySpend: 2000,
            accountAge: 60, // days
            applicationStatus: 'approved'
        }
    }
};

// Behavioral patterns that influence persona classification
const BEHAVIORAL_PATTERNS = {
    HIGH_ENGAGEMENT: {
        sessionFrequency: 'daily',
        averageSessionDuration: 1800, // 30 minutes
        featuresUsed: 10,
        supportInteractions: 'low'
    },
    RESEARCH_FOCUSED: {
        pagesPerSession: 15,
        timeOnProductPages: 300, // 5 minutes
        searchFrequency: 'high',
        comparisonBehavior: 'high'
    },
    TRANSACTION_FOCUSED: {
        conversionRate: 0.8,
        averageOrderValue: 'high',
        repeatPurchaseRate: 0.6,
        cartAbandonmentRate: 'low'
    },
    SUPPORT_DEPENDENT: {
        supportTickets: 'high',
        aiAssistantUsage: 'high',
        documentationViews: 'high',
        onboardingCompletion: 'low'
    }
};

// Advanced persona classification system
class PersonaClassifier {
    constructor() {
        this.classificationCache = new Map();
        this.modelWeights = this.initializeModelWeights();
    }

    // Initialize machine learning model weights
    initializeModelWeights() {
        return {
            businessMetrics: 0.4,      // Revenue, volume, size indicators
            behavioralPatterns: 0.3,   // Usage patterns, engagement
            demographics: 0.2,         // Location, industry experience
            temporalFactors: 0.1       // Account age, seasonality
        };
    }

    // Main persona classification function
    async classifyUserPersona(userId) {
        // Input validation
        if (!userId) {
            throw new Error('User ID is required for persona classification');
        }

        // Convert to string if it's an ObjectId
        const userIdString = userId.toString();

        try {
            // Check cache first
            if (this.classificationCache.has(userIdString)) {
                const cached = this.classificationCache.get(userIdString);
                if (Date.now() - cached.timestamp < 3600000) { // 1 hour cache
                    return cached.classification;
                }
            }

            // Gather comprehensive user data
            const userData = await this.gatherUserData(userIdString);

            // Validate user data
            if (!userData) {
                return this.getDefaultClassification();
            }

            // Calculate persona scores
            const personaScores = await this.calculatePersonaScores(userData);

            // Validate scores
            if (!personaScores || Object.keys(personaScores).length === 0) {
                return this.getDefaultClassification();
            }

            // Determine primary and secondary personas
            const classification = this.determinePersonaClassification(personaScores, userData);

            // Cache the result
            this.classificationCache.set(userIdString, {
                classification,
                timestamp: Date.now()
            });

            return classification;

        } catch (error) {
            console.error('Error classifying user persona:', error);
            // Return default classification instead of throwing
            return this.getDefaultClassification();
        }
    }

    // Get default classification for error cases
    getDefaultClassification() {
        return {
            primaryPersona: {
                key: 'STARTUP_VENDOR',
                name: 'New User',
                description: 'User with limited data available for classification',
                score: 0.1,
                confidence: 0.1
            },
            secondaryPersona: null,
            allScores: {},
            classificationDate: new Date(),
            dataQuality: 'low'
        };
    }

    // Gather comprehensive user data for classification
    async gatherUserData(userId) {
        try {
            let baseProfile = null;
            let behaviorData = null;
            let enhancedInsights = null;

            // Safely gather base profile
            try {
                baseProfile = await userAnalytics.buildUserAnalyticsProfile(userId);
            } catch (error) {
                console.warn('Error getting base profile for persona classification:', error);
            }

            // Safely gather behavior data
            try {
                behaviorData = userAnalytics.getUserBehavior(userId);
            } catch (error) {
                console.warn('Error getting behavior data for persona classification:', error);
            }

            // Safely gather enhanced insights
            try {
                enhancedInsights = await enhancedTracking.getEnhancedUserInsights(userId);
                if (enhancedInsights && enhancedInsights.error) {
                    enhancedInsights = null;
                }
            } catch (error) {
                console.warn('Error getting enhanced insights for persona classification:', error);
            }

            // Return combined data, even if some parts are missing
            return {
                ...baseProfile,
                behaviorData,
                enhancedInsights
            };
        } catch (error) {
            console.error('Error gathering user data for classification:', error);
            return null;
        }
    }

    // Calculate scores for each persona based on user data
    async calculatePersonaScores(userData) {
        const scores = {};
        
        // Calculate scores for each defined persona
        for (const [personaKey, personaDefinition] of Object.entries(BUYER_PERSONAS)) {
            scores[personaKey] = this.calculatePersonaScore(userData, personaDefinition);
        }
        
        return scores;
    }

    // Calculate individual persona score
    calculatePersonaScore(userData, personaDefinition) {
        let score = 0;
        let maxScore = 0;
        
        // Business metrics scoring
        const businessScore = this.scoreBusinessMetrics(userData, personaDefinition.thresholds);
        score += businessScore * this.modelWeights.businessMetrics;
        maxScore += this.modelWeights.businessMetrics;
        
        // Behavioral patterns scoring
        const behaviorScore = this.scoreBehavioralPatterns(userData, personaDefinition.characteristics);
        score += behaviorScore * this.modelWeights.behavioralPatterns;
        maxScore += this.modelWeights.behavioralPatterns;
        
        // Demographics scoring
        const demographicScore = this.scoreDemographics(userData, personaDefinition);
        score += demographicScore * this.modelWeights.demographics;
        maxScore += this.modelWeights.demographics;
        
        // Temporal factors scoring
        const temporalScore = this.scoreTemporalFactors(userData, personaDefinition.thresholds);
        score += temporalScore * this.modelWeights.temporalFactors;
        maxScore += this.modelWeights.temporalFactors;
        
        return maxScore > 0 ? score / maxScore : 0;
    }

    // Score business metrics against persona thresholds
    scoreBusinessMetrics(userData, thresholds) {
        let score = 0;
        let factors = 0;
        
        const metrics = userData.businessMetrics || {};
        
        // Product count scoring
        if (thresholds.productCount && metrics.productCount !== undefined) {
            score += this.scoreThreshold(metrics.productCount, thresholds.productCount, 'gte');
            factors++;
        }
        
        // Revenue scoring
        if (thresholds.monthlyRevenue && metrics.totalRevenue !== undefined) {
            const monthlyRevenue = metrics.totalRevenue / Math.max(userData.demographics.accountAge / 30, 1);
            score += this.scoreThreshold(monthlyRevenue, thresholds.monthlyRevenue, 'gte');
            factors++;
        }
        
        // Order volume scoring
        if (thresholds.monthlyOrders && metrics.orderCount !== undefined) {
            const monthlyOrders = metrics.orderCount / Math.max(userData.demographics.accountAge / 30, 1);
            score += this.scoreThreshold(monthlyOrders, thresholds.monthlyOrders, 'gte');
            factors++;
        }
        
        // Spending scoring
        if (thresholds.monthlySpend && metrics.totalSpent !== undefined) {
            const monthlySpend = metrics.totalSpent / Math.max(userData.demographics.accountAge / 30, 1);
            score += this.scoreThreshold(monthlySpend, thresholds.monthlySpend, 'gte');
            factors++;
        }
        
        return factors > 0 ? score / factors : 0;
    }

    // Score behavioral patterns
    scoreBehavioralPatterns(userData, characteristics) {
        let score = 0;
        let factors = 0;
        
        const behaviorData = userData.behaviorData;
        if (!behaviorData) return 0;
        
        // Engagement level scoring
        if (characteristics.includes('high_engagement')) {
            const engagementScore = this.calculateEngagementScore(behaviorData);
            score += engagementScore;
            factors++;
        }
        
        // Volume-based characteristics
        if (characteristics.includes('high_volume')) {
            const volumeScore = this.calculateVolumeScore(userData);
            score += volumeScore;
            factors++;
        }
        
        // Quality focus characteristics
        if (characteristics.includes('quality_over_quantity')) {
            const qualityScore = this.calculateQualityScore(userData);
            score += qualityScore;
            factors++;
        }
        
        return factors > 0 ? score / factors : 0;
    }

    // Score demographics
    scoreDemographics(userData, personaDefinition) {
        let score = 0;
        let factors = 0;
        
        const demographics = userData.enhancedInsights?.demographics;
        if (!demographics) return 0.5; // Neutral score if no demographic data
        
        // Business experience scoring
        if (demographics.industryExperience) {
            const experienceScore = this.scoreExperience(demographics.industryExperience, personaDefinition);
            score += experienceScore;
            factors++;
        }
        
        // Company size scoring
        if (demographics.companySize) {
            const sizeScore = this.scoreCompanySize(demographics.companySize, personaDefinition);
            score += sizeScore;
            factors++;
        }
        
        return factors > 0 ? score / factors : 0.5;
    }

    // Score temporal factors
    scoreTemporalFactors(userData, thresholds) {
        let score = 0;
        let factors = 0;
        
        // Account age scoring
        if (thresholds.accountAge && userData.demographics.accountAge !== undefined) {
            score += this.scoreThreshold(userData.demographics.accountAge, thresholds.accountAge, 'gte');
            factors++;
        }
        
        // Application status scoring
        if (thresholds.applicationStatus && userData.businessMetrics.applicationStatus) {
            const statusMatch = userData.businessMetrics.applicationStatus === thresholds.applicationStatus;
            score += statusMatch ? 1 : 0;
            factors++;
        }
        
        return factors > 0 ? score / factors : 0.5;
    }

    // Helper function to score against thresholds
    scoreThreshold(value, threshold, operator = 'gte') {
        switch (operator) {
            case 'gte':
                return value >= threshold ? 1 : Math.min(value / threshold, 1);
            case 'lte':
                return value <= threshold ? 1 : Math.max(threshold / value, 0);
            case 'eq':
                return value === threshold ? 1 : 0;
            default:
                return 0;
        }
    }

    // Calculate engagement score from behavior data
    calculateEngagementScore(behaviorData) {
        const recentEvents = behaviorData.events.filter(e => 
            Date.now() - e.timestamp.getTime() < 7 * 24 * 60 * 60 * 1000 // Last 7 days
        );
        
        const sessionCount = new Set(recentEvents.map(e => e.sessionId)).size;
        const eventCount = recentEvents.length;
        
        // Normalize scores
        const sessionScore = Math.min(sessionCount / 7, 1); // Daily sessions = 1
        const eventScore = Math.min(eventCount / 50, 1); // 50+ events = 1
        
        return (sessionScore + eventScore) / 2;
    }

    // Calculate volume score
    calculateVolumeScore(userData) {
        const metrics = userData.businessMetrics || {};
        
        if (userData.userRole === 'vendor') {
            return Math.min((metrics.productCount || 0) / 100, 1);
        } else if (userData.userRole === 'retailer') {
            return Math.min((metrics.orderCount || 0) / 50, 1);
        }
        
        return 0;
    }

    // Calculate quality focus score
    calculateQualityScore(userData) {
        const metrics = userData.businessMetrics || {};
        
        if (userData.userRole === 'vendor') {
            const avgPrice = metrics.averageProductPrice || 0;
            return Math.min(avgPrice / 200, 1); // $200+ average = quality focus
        }
        
        return 0.5; // Neutral for retailers
    }

    // Score experience level
    scoreExperience(experience, personaDefinition) {
        const experienceMap = {
            'beginner': 0.2,
            'intermediate': 0.5,
            'experienced': 0.8,
            'expert': 1.0
        };
        
        return experienceMap[experience] || 0.5;
    }

    // Score company size
    scoreCompanySize(size, personaDefinition) {
        const sizeMap = {
            'micro': 0.2,
            'small': 0.4,
            'medium': 0.6,
            'large': 0.8,
            'enterprise': 1.0
        };
        
        return sizeMap[size] || 0.5;
    }

    // Determine final persona classification
    determinePersonaClassification(personaScores, userData) {
        // Sort personas by score
        const sortedPersonas = Object.entries(personaScores)
            .sort(([,a], [,b]) => b - a);
        
        const primaryPersona = sortedPersonas[0];
        const secondaryPersona = sortedPersonas[1];
        
        return {
            primaryPersona: {
                key: primaryPersona[0],
                name: BUYER_PERSONAS[primaryPersona[0]].name,
                description: BUYER_PERSONAS[primaryPersona[0]].description,
                score: primaryPersona[1],
                confidence: this.calculateConfidence(primaryPersona[1], secondaryPersona[1])
            },
            secondaryPersona: secondaryPersona[1] > 0.3 ? {
                key: secondaryPersona[0],
                name: BUYER_PERSONAS[secondaryPersona[0]].name,
                score: secondaryPersona[1]
            } : null,
            allScores: personaScores,
            classificationDate: new Date(),
            dataQuality: this.assessDataQuality(userData)
        };
    }

    // Calculate confidence in classification
    calculateConfidence(primaryScore, secondaryScore) {
        const scoreDifference = primaryScore - secondaryScore;
        const confidence = Math.min(primaryScore + scoreDifference, 1);
        return Math.max(confidence, 0.1); // Minimum 10% confidence
    }

    // Assess quality of data used for classification
    assessDataQuality(userData) {
        let qualityScore = 0;
        let factors = 0;
        
        // Business metrics availability
        if (userData.businessMetrics && Object.keys(userData.businessMetrics).length > 0) {
            qualityScore += 0.3;
        }
        factors += 0.3;
        
        // Behavioral data availability
        if (userData.behaviorData && userData.behaviorData.events.length > 10) {
            qualityScore += 0.3;
        }
        factors += 0.3;
        
        // Enhanced insights availability
        if (userData.enhancedInsights && !userData.enhancedInsights.error) {
            qualityScore += 0.2;
        }
        factors += 0.2;
        
        // Account age (more data over time)
        if (userData.demographics.accountAge > 30) {
            qualityScore += 0.2;
        }
        factors += 0.2;
        
        const quality = qualityScore / factors;
        
        if (quality > 0.8) return 'high';
        if (quality > 0.5) return 'medium';
        return 'low';
    }

    // Get persona recommendations for marketing
    getPersonaRecommendations(classification) {
        const persona = BUYER_PERSONAS[classification.primaryPersona.key];
        const recommendations = [];
        
        // Marketing channel recommendations
        if (persona.characteristics.includes('high_volume')) {
            recommendations.push({
                type: 'marketing_channel',
                recommendation: 'Focus on B2B channels and trade publications',
                priority: 'high'
            });
        }
        
        if (persona.characteristics.includes('new_to_market')) {
            recommendations.push({
                type: 'content_strategy',
                recommendation: 'Provide educational content and onboarding support',
                priority: 'high'
            });
        }
        
        if (persona.characteristics.includes('premium_focus')) {
            recommendations.push({
                type: 'messaging',
                recommendation: 'Emphasize quality, craftsmanship, and exclusivity',
                priority: 'medium'
            });
        }
        
        return recommendations;
    }
}

// Export singleton instance
const personaClassifier = new PersonaClassifier();

module.exports = {
    personaClassifier,
    BUYER_PERSONAS,
    BEHAVIORAL_PATTERNS
};
