<template>
    <div class="page" v-cloak>
        <header-component></header-component>
        <div class="d-table-row">
            <div class="d-table-cell text-top">
                <verify-email class="my-6"></verify-email>
                <application-block class="my-6" v-if="$route.path !== '/application'"></application-block>
            </div>
        </div>
        <div class="content-row">
            <router-view></router-view>
        </div>
        <footer-component role="retailer" :is-fixed="isFooterFixed"></footer-component>
        <modal-component ref="modal"></modal-component>
        <ai-assistant></ai-assistant>
    </div>
</template>

<script>
import ApplicationBlock from '@/retailer/components/homePage/ApplicationBlock';
import FooterComponent from '@/common/components/Footer';
import VerifyEmail from '@/common/components/VerifyEmail';
import { FlashService } from '@/common/FlashService';
import HttpPlugin from '@/common/plugins/httpPlugin';
import TermsAndConditionsModal from '@/retailer/components/TermsAndConditionsModal';
import cardValidator from 'card-validator';
import { Validator } from 'vee-validate';
import Vue from 'vue';
import { sync } from 'vuex-router-sync';
import createRouter from '../router';
import store, {
    orderService,
    productService,
    retailerService,
    ticketService,
    userService,
    vendorService,
    loggerService
} from '../store';
import HeaderComponent from './Header';
import AIAssistant from '@/common/components/AIAssistant';

Vue.use(HttpPlugin, { store });

Validator.extend('cardNumber', {
    getMessage: () => 'Card number is invalid',
    validate: value => cardValidator.number(value).isValid
});
Validator.extend('expireDate', {
    getMessage: () => 'Date is invalid',
    validate: value => cardValidator.expirationDate(value).isValid
});
Validator.extend('cvc', {
    getMessage: () => 'CVC is invalid',
    validate: value => Number.isInteger(Number(value)) && String(value).length >= 3 && String(value).length <= 4
});

const router = createRouter(store);
router.afterEach((to) => {
    loggerService.pageView({ page: to.path });
});
sync(store, router, { moduleName: 'RouteModule' });

const flashService = new FlashService();

export default {
    name: 'RetailerModule',
    provide: {
        $productService: productService,
        $userService: userService,
        $retailerService: retailerService,
        $orderService: orderService,
        $ticketService: ticketService,
        $vendorService: vendorService,
        $logger: loggerService
    },
    components: {
        ApplicationBlock,
        VerifyEmail,
        HeaderComponent,
        FooterComponent,
        AIAssistant
    },
    router,
    store,
    mounted() {
        this.$modal.setTarget(this.$refs.modal);
        flashService.setTarget(this.$refs.flash);

        if (!store.state.shadowLogin && store.state.retailer.needAcceptNewTerms) {
            this.$modal.open(TermsAndConditionsModal, {
                size: 'modal-800',
                disableClose: true
            });
        }
    },
    computed: {
        isFooterFixed() {
            return this.$route.name === 'search' || this.$route.name === 'product';
        }
    }
};
</script>
