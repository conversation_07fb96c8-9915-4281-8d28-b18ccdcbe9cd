<template>
    <div class="page" id="admin-module">
        <header-component/>
        <div class="content-row">
            <div class="d-table-cell text-top">
                <div class="container-fluid max-w-lg mt-3 mb-3">
                    <div class="row-condensed">
                        <menu-component/>
                        <div class="visible-sm-block visible-xs-block mt-3"></div>
                        <div class="col-md-10">
                            <router-view :key="$route.path + $store.state.currentState"></router-view>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <footer-component/>
        <modal-component ref="modal"></modal-component>
        <ai-assistant></ai-assistant>
    </div>
</template>

<script>
import HttpPlugin from '@/common/plugins/httpPlugin';
import Vue from 'vue';
import {sync} from 'vuex-router-sync';
import router from '../router';
import store, {
    adminService,
    applicationService,
    emailBlastService,
    exportService,
    orderService,
    productService,
    retailerService,
    reviewService,
    ticketService,
    userService,
    statesService,
    documentsService,
    regulatoryBodyService,
    storeService,
    vendorService
} from '../store';
import FooterComponent from './Footer';
import HeaderComponent from './Header';
import MenuComponent from './Menu';
import AIAssistant from '@/common/components/AIAssistant';

Vue.use(HttpPlugin, {store});
sync(store, router, { moduleName: 'RouteModule' } );

export default {
    name: 'AdminModule',
    provide: {
        $userService: userService,
        $statesService: statesService,
        $documentsService: documentsService,
        $regulatoryBodyService: regulatoryBodyService,
        $adminService: adminService,
        $applicationService: applicationService,
        $orderService: orderService,
        $productService: productService,
        $retailerService: retailerService,
        $reviewService: reviewService,
        $ticketService: ticketService,
        $vendorService: vendorService,
        $storeService: storeService,
        $emailBlastService: emailBlastService,
        $exportService: exportService
    },
    components: {
        HeaderComponent,
        MenuComponent,
        FooterComponent,
        AIAssistant
    },
    router,
    store,
    mounted() {
        this.$modal.setTarget(this.$refs.modal);
    }
};
</script>
