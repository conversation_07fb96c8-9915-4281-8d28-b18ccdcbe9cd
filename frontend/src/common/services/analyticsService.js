/**
 * Analytics Service for tracking user behavior and interactions
 */
export class AnalyticsService {
    constructor(http) {
        this.http = http;
        this.sessionId = this.generateSessionId();
        this.pageStartTime = Date.now();
        this.isTrackingEnabled = true;
        
        // Initialize page tracking
        this.initializePageTracking();
    }

    // Generate unique session ID
    generateSessionId() {
        return `session_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    }

    // Initialize automatic page tracking
    initializePageTracking() {
        // Track page views on route changes
        if (typeof window !== 'undefined') {
            // Track initial page load
            this.trackPageView(window.location.pathname);
            
            // Track page visibility changes
            document.addEventListener('visibilitychange', () => {
                if (document.hidden) {
                    this.trackPageExit();
                } else {
                    this.trackPageView(window.location.pathname);
                }
            });
            
            // Track before page unload
            window.addEventListener('beforeunload', () => {
                this.trackPageExit();
            });
        }
    }

    // Enable/disable tracking
    setTrackingEnabled(enabled) {
        this.isTrackingEnabled = enabled;
    }

    // Generic event tracking
    async trackEvent(eventType, eventData = {}) {
        if (!this.isTrackingEnabled) return;
        
        try {
            const response = await this.http.request({
                url: '/analytics/track',
                method: 'POST',
                data: {
                    eventType,
                    eventData: {
                        ...eventData,
                        sessionId: this.sessionId,
                        timestamp: new Date().toISOString()
                    }
                }
            });
            
            return response;
        } catch (error) {
            console.warn('Analytics tracking failed:', error);
            return null;
        }
    }

    // Track page views
    async trackPageView(pagePath, additionalData = {}) {
        if (!this.isTrackingEnabled) return;
        
        const timeOnPreviousPage = Date.now() - this.pageStartTime;
        this.pageStartTime = Date.now();
        
        try {
            const response = await this.http.request({
                url: '/analytics/track/page-view',
                method: 'POST',
                data: {
                    pagePath,
                    timeOnPage: timeOnPreviousPage > 100 ? timeOnPreviousPage : 0, // Ignore very short times
                    scrollDepth: this.getScrollDepth(),
                    ...additionalData
                }
            });
            
            return response;
        } catch (error) {
            console.warn('Page view tracking failed:', error);
            return null;
        }
    }

    // Track page exit
    trackPageExit() {
        const timeOnPage = Date.now() - this.pageStartTime;
        const scrollDepth = this.getScrollDepth();
        
        // Use sendBeacon for reliable tracking on page exit
        if (navigator.sendBeacon && this.isTrackingEnabled) {
            const data = JSON.stringify({
                eventType: 'page_exit',
                eventData: {
                    pagePath: window.location.pathname,
                    timeOnPage,
                    scrollDepth,
                    sessionId: this.sessionId
                }
            });
            
            navigator.sendBeacon('/analytics/track', data);
        }
    }

    // Track search queries
    async trackSearch(searchQuery, results = [], filters = {}, sortBy = 'relevance') {
        return await this.http.request({
            url: '/analytics/track/search',
            method: 'POST',
            data: {
                searchQuery,
                results,
                filters,
                sortBy
            }
        });
    }

    // Track product interactions
    async trackProductInteraction(productId, interactionType, additionalData = {}) {
        return await this.http.request({
            url: '/analytics/track/product-interaction',
            method: 'POST',
            data: {
                productId,
                interactionType, // 'view', 'click', 'add_to_cart', 'contact_vendor'
                ...additionalData
            }
        });
    }

    // Track business actions
    async trackBusinessAction(actionType, actionData = {}) {
        return await this.http.request({
            url: '/analytics/track/business-action',
            method: 'POST',
            data: {
                actionType,
                actionData
            }
        });
    }

    // Track AI assistant usage
    async trackAIAssistantUsage(interactionType, additionalData = {}) {
        return await this.http.request({
            url: '/analytics/track/ai-assistant',
            method: 'POST',
            data: {
                interactionType,
                ...additionalData
            }
        });
    }

    // Track form interactions
    async trackFormInteraction(formName, interactionType, fieldName = null) {
        return await this.trackEvent('form_interaction', {
            formName,
            interactionType, // 'focus', 'blur', 'submit', 'error'
            fieldName
        });
    }

    // Track button clicks
    async trackButtonClick(buttonName, buttonType = 'button', context = {}) {
        return await this.trackEvent('button_click', {
            buttonName,
            buttonType,
            context
        });
    }

    // Track errors
    async trackError(errorType, errorMessage, context = {}) {
        return await this.trackEvent('error_encountered', {
            errorType,
            errorMessage,
            context,
            userAgent: navigator.userAgent,
            url: window.location.href
        });
    }

    // Track feature usage
    async trackFeatureUsage(featureName, action, metadata = {}) {
        return await this.trackEvent('feature_usage', {
            featureName,
            action,
            metadata
        });
    }

    // Get user insights
    async getUserInsights() {
        try {
            const response = await this.http.request({
                url: '/analytics/user/me/insights',
                method: 'GET'
            });
            
            return response;
        } catch (error) {
            console.warn('Failed to get user insights:', error);
            return null;
        }
    }

    // Get user persona
    async getUserPersona() {
        try {
            const response = await this.http.request({
                url: '/analytics/user/me/persona',
                method: 'GET'
            });

            return response;
        } catch (error) {
            console.warn('Failed to get user persona:', error);
            return null;
        }
    }

    // Get detailed persona classification
    async getPersonaClassification() {
        try {
            const response = await this.http.request({
                url: '/analytics/user/me/persona-classification',
                method: 'GET'
            });

            return response;
        } catch (error) {
            console.warn('Failed to get persona classification:', error);
            return null;
        }
    }

    // Get persona-based recommendations
    async getPersonaRecommendations() {
        try {
            const response = await this.http.request({
                url: '/analytics/user/me/persona-recommendations',
                method: 'GET'
            });

            return response;
        } catch (error) {
            console.warn('Failed to get persona recommendations:', error);
            return null;
        }
    }

    // Track demographic information
    async trackDemographicUpdate(demographicData) {
        return await this.http.request({
            url: '/analytics/track/demographic',
            method: 'POST',
            data: { demographicData }
        });
    }

    // Track business size detection
    async trackBusinessSize(detectedSize, indicators) {
        return await this.http.request({
            url: '/analytics/track/business-size',
            method: 'POST',
            data: { detectedSize, indicators }
        });
    }

    // Track browsing patterns
    async trackBrowsingPattern(patternType, patternData) {
        return await this.http.request({
            url: '/analytics/track/browsing-pattern',
            method: 'POST',
            data: { patternType, patternData }
        });
    }

    // Track device fingerprint
    async trackDeviceFingerprint(deviceData) {
        return await this.http.request({
            url: '/analytics/track/device-fingerprint',
            method: 'POST',
            data: { deviceData }
        });
    }

    // Track location data
    async trackLocationData(locationData) {
        return await this.http.request({
            url: '/analytics/track/location',
            method: 'POST',
            data: { locationData }
        });
    }

    // Track referral source
    async trackReferralSource(referralData) {
        return await this.http.request({
            url: '/analytics/track/referral',
            method: 'POST',
            data: { referralData }
        });
    }

    // Track engagement milestone
    async trackEngagementMilestone(milestone, milestoneData) {
        return await this.http.request({
            url: '/analytics/track/engagement-milestone',
            method: 'POST',
            data: { milestone, milestoneData }
        });
    }

    // Track conversion funnel step
    async trackConversionFunnelStep(funnelStep, stepData) {
        return await this.http.request({
            url: '/analytics/track/conversion-funnel',
            method: 'POST',
            data: { funnelStep, stepData }
        });
    }

    // Helper methods
    getScrollDepth() {
        if (typeof window === 'undefined') return 0;
        
        const scrollTop = window.pageYOffset || document.documentElement.scrollTop;
        const documentHeight = document.documentElement.scrollHeight - window.innerHeight;
        
        if (documentHeight <= 0) return 100;
        
        return Math.round((scrollTop / documentHeight) * 100);
    }

    // Track conversion events
    async trackConversion(conversionType, value = 0, metadata = {}) {
        return await this.trackEvent('conversion', {
            conversionType,
            value,
            metadata
        });
    }

    // Track engagement milestones
    async trackEngagementMilestone(milestone, metadata = {}) {
        return await this.trackEvent('engagement_milestone', {
            milestone,
            metadata
        });
    }

    // Batch tracking for performance
    async trackBatch(events) {
        if (!this.isTrackingEnabled || !events.length) return;
        
        try {
            const batchData = events.map(event => ({
                ...event,
                sessionId: this.sessionId,
                timestamp: new Date().toISOString()
            }));
            
            const response = await this.http.request({
                url: '/analytics/track/batch',
                method: 'POST',
                data: { events: batchData }
            });
            
            return response;
        } catch (error) {
            console.warn('Batch tracking failed:', error);
            return null;
        }
    }

    // Track user journey milestones
    async trackJourneyMilestone(milestone, stage, metadata = {}) {
        return await this.trackEvent('journey_milestone', {
            milestone,
            stage, // 'awareness', 'consideration', 'decision', 'retention'
            metadata
        });
    }

    // Track A/B test participation
    async trackABTest(testName, variant, metadata = {}) {
        return await this.trackEvent('ab_test', {
            testName,
            variant,
            metadata
        });
    }

    // Performance tracking
    async trackPerformance(metricName, value, metadata = {}) {
        return await this.trackEvent('performance_metric', {
            metricName,
            value,
            metadata
        });
    }
}

// Vue plugin for easy integration
export default {
    install(Vue, options = {}) {
        const analyticsService = new AnalyticsService(options.http);
        
        // Make available globally
        Vue.prototype.$analytics = analyticsService;
        
        // Add global mixin for automatic tracking
        Vue.mixin({
            mounted() {
                // Track component mounting
                if (this.$options.name && options.trackComponents) {
                    analyticsService.trackEvent('component_mounted', {
                        componentName: this.$options.name
                    });
                }
            },
            
            beforeDestroy() {
                // Track component destruction
                if (this.$options.name && options.trackComponents) {
                    analyticsService.trackEvent('component_destroyed', {
                        componentName: this.$options.name
                    });
                }
            }
        });
        
        // Track route changes
        if (options.router) {
            options.router.afterEach((to, from) => {
                analyticsService.trackPageView(to.path, {
                    fromPath: from.path,
                    routeName: to.name
                });
            });
        }
    }
};
