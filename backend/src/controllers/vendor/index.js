"use strict";

const config = require("../../services/config");
const mongoose = require("mongoose");
const ObjectId = mongoose.Types.ObjectId;
const Bluebird = require("bluebird");
const constants = require("../../models/constants");
const { HttpError } = require("../../services/error");
const { vendorSpa } = require("./middleware");
const Router = require("koa-router");
const campaignTriggers = require("../../services/campaign-triggers");

const vendorRouter = Router()
  .prefix('/vendor')
  .use(vendorSpa)
  .get('/statistics', getStatistics)
  .get('/application', getApplication)
  .post('/application', postApplication)
  .put('/application', putApplication)
  .post('/application/add-data', postUpdateApplication)
  .post('/switch', postSwitchAccount)
  .patch('/terms-and-conditions', patchTermsAndConditions)
  .use(require('./orders').routes())
  .use(require('./products').routes())
  .use(require('./product-import').routes())
  .use(require('./settings').routes())
  .use(require('./ticket').routes())
  .use(require('./coupon').routes())
  .get('*', () => ({}));


module.exports = vendorRouter;

async function getApplication(ctx) {
  const { Application } = mongoose.models;
  const { vendor } = ctx.state;

  const application = await Application.getForVendor(vendor);

  if (application) {
    ctx.body = {
      data: {
        ok: true,
        application: { ...application._doc, ein: application.ein }
      },

    };
  } else {
    ctx.body = {
      data: {
        ok: false,
        message: "No Application Found"
      }
    };
  }
}

async function putApplication(ctx) {
  const { SystemLog } = mongoose.models;
  const { Application } = mongoose.models;
  const { vendor } = ctx.state;
  const { businessName, tradeName, ein, legalAddress, fullName, phone, license, files,
    description, monthlySales, proofOfAddress, personalId,
    agreeTermsOfServiceAndPrivacyPolicy, agreeUserFeesAndPaymentPolicy, agreeReturnPolicy, forDraft } = ctx.request.body;

  try {
    const updatedApplication = await Application.updateForVendor({
      businessName, tradeName, ein, legalAddress, fullName, phone, license, files,
      description, monthlySales, proofOfAddress, personalId,
      agreeTermsOfServiceAndPrivacyPolicy, agreeUserFeesAndPaymentPolicy, agreeReturnPolicy, vendor, forDraft
    });

    await SystemLog.vendorApplicationUpdated(ctx, updatedApplication);

    ctx.body = {
      data: {
        application: updatedApplication.formatPublic()
      }
    };
  } catch (err) {
    console.log('there was an error updating the application.', err);
    // Properly handle and return the error to the client
    if (err.code === 11000) {
      throw new HttpError(422, 'Duplicate key error', { error: 'A record with this information already exists' });
    } else if (err.name === 'ValidationError') {
      throw new HttpError(422, 'Validation error', err.errors);
    } else {
      throw new HttpError(500, 'Error updating application', { error: err.message });
    }
  }
}

async function postApplication(ctx) {
  const { SystemLog } = mongoose.models;
  const { Application } = mongoose.models;
  const { businessName, tradeName, ein, legalAddress, fullName, phone, license, files,
    description, monthlySales, proofOfAddress, personalId,
    agreeTermsOfServiceAndPrivacyPolicy, agreeUserFeesAndPaymentPolicy, agreeReturnPolicy, isDraft } = ctx.request.body;
  const { vendor } = ctx.state;

  // if (!constants.einRegex.test(ein)) {
  //     throw new HttpError(422, 'Invalid ein', {ein: 'invalid number'});
  // }

  try {
    const application = await Application.createForVendor({
      vendor,
      businessName,
      tradeName,
      ein,
      legalAddress,
      fullName,
      phone,
      license,
      files,
      description, monthlySales, proofOfAddress, personalId,
      agreeTermsOfServiceAndPrivacyPolicy,
      agreeUserFeesAndPaymentPolicy,
      agreeReturnPolicy,
      isDraft
    });

    SystemLog.vendorApplicationCreated(ctx, application);

    // Trigger HubSpot campaign for application submission
    try {
      await campaignTriggers.triggerCampaign(campaignTriggers.TRIGGER_EVENTS.APPLICATION_SUBMITTED, {
        userId: ctx.state.user._id,
        applicationType: 'vendor',
        applicationId: application._id,
        businessName,
        isDraft
      });
    } catch (error) {
      console.error('Error triggering application submitted campaign:', error);
      // Don't fail the main request if campaign trigger fails
    }

    ctx.body = {
      data: {
        application: application.formatPublic()
      }
    };
  }
  catch (err) {
    console.log('there was an error creating the application.', err);
    // Properly handle and return the error to the client
    if (err.code === 11000) {
      throw new HttpError(422, 'Duplicate key error', { error: 'A record with this information already exists' });
    } else if (err.name === 'ValidationError') {
      throw new HttpError(422, 'Validation error', err.errors);
    } else {
      throw new HttpError(500, 'Error creating application', { error: err.message });
    }
  }
}

async function postUpdateApplication(ctx) {
  const { Application } = mongoose.models;
  const { ein, files } = ctx.request.body;
  const { vendor } = ctx.state;

  const application = await Application.findOne({
    _id: vendor._application,
  }).sort({ _id: -1 });

  if (!application) {
    throw new HttpError(422, "Application not found");
  }
  if (!ein) {
    throw new HttpError(422, "Required ein", { ein: "required" });
  }
  // if (!constants.einRegex.test(ein)) {
  //     throw new HttpError(422, 'Invalid ein', {ein: 'invalid number'});
  // }

  application.set({
    ein,
    files: (application.files || []).concat(files),
  });
  await application.save();

  ctx.body = {
    data: {
      application: application.formatPublic(),
    },
  };
}

async function patchTermsAndConditions(ctx) {
  const { SystemLog } = mongoose.models;
  const { vendor } = ctx.state;

  vendor.set({ lastAgreeTermsAt: new Date() });
  await vendor.save();
  await SystemLog.vendorTermsAgreed(ctx, vendor);

  ctx.body = {
    data: vendor.formatSource(),
  };
}

async function postSwitchAccount(ctx) {
  const { user } = ctx.state;
  const { key } = ctx.request.body;

  const role = await user.switchRetailerAndVendorAccounts(key);

  ctx.body = {
    data: "/" + role,
  };
}

async function getStatistics(ctx) {
  const { vendor } = ctx.state;

  const response = await Bluebird.props({
    account: accountStatistics(vendor),
    products: productStatistics(vendor),
    orders: orderStatistics(vendor),
    tickets: ticketStatistics(vendor),
  });

  ctx.body = {
    data: response,
  };
}

function accountStatistics(vendor) {
  const { ProductOrder } = mongoose.models;
  return ProductOrder.aggregate([
    {
      $match: {
        _vendor: vendor._id,
        status: { $in: constants.PRODUCT_ORDER.VENDOR_STATUSES },
      },
    },
    {
      $group: {
        _id: "$status",
        earningPrice: { $sum: "$earningPrice" },
      },
    },
  ]).then((array) => {
    let sold = 0,
      paid = 0;
    array.forEach(({ _id: status, earningPrice }) => {
      if (status === constants.PRODUCT_ORDER.STATUS.PAIDOUT) {
        paid += earningPrice;
        sold += earningPrice;
      } else if (
        [
          constants.PRODUCT_ORDER.STATUS.APPROVED,
          constants.PRODUCT_ORDER.STATUS.PREPARE_FOR_SHIPMENT,
          constants.PRODUCT_ORDER.STATUS.SHIPPED,
          constants.PRODUCT_ORDER.STATUS.DELIVERED,
        ].includes(status)
      ) {
        sold += earningPrice;
      }
    });
    return {
      sold,
      paid,
      pending: sold - paid,
    };
  });
}

function orderStatistics(vendor) {
  const { ProductOrder } = mongoose.models;
  return ProductOrder.aggregate([
    {
      $match: {
        _vendor: vendor._id,
        status: { $in: constants.PRODUCT_ORDER.VENDOR_STATUSES },
      },
    },
    {
      $group: {
        _id: null,
        new: {
          $sum: {
            $cond: {
              if: { $eq: ["$status", constants.PRODUCT_ORDER.STATUS.APPROVED] },
              then: 1,
              else: 0,
            },
          },
        },
        prepare: {
          $sum: {
            $cond: {
              if: {
                $and: [
                  {
                    $eq: [
                      "$status",
                      constants.PRODUCT_ORDER.STATUS.PREPARE_FOR_SHIPMENT,
                    ],
                  },
                ],
              },
              then: 1,
              else: 0,
            },
          },
        },
        shipped: {
          $sum: {
            $cond: {
              if: {
                $and: [
                  { $eq: ["$status", constants.PRODUCT_ORDER.STATUS.SHIPPED] },
                ],
              },
              then: 1,
              else: 0,
            },
          },
        },
        delivered: {
          $sum: {
            $cond: {
              if: {
                $and: [
                  {
                    $eq: ["$status", constants.PRODUCT_ORDER.STATUS.DELIVERED],
                  },
                ],
              },
              then: 1,
              else: 0,
            },
          },
        },
        canceled: {
          $sum: {
            $cond: {
              if: {
                $and: [
                  {
                    $eq: ["$status", constants.PRODUCT_ORDER.STATUS.CANCELLED],
                  },
                ],
              },
              then: 1,
              else: 0,
            },
          },
        },
      },
    },
    {
      $project: {
        _id: 0,
      },
    },
  ]).then(
    (array) =>
      array[0] || { new: 0, prepare: 0, shipped: 0, delivered: 0, canceled: 0 }
  );
}

function productStatistics(vendor) {
  const { Product } = mongoose.models;
  return Product.aggregate([
    {
      $match: {
        _vendor: vendor._id,
      },
    },
    {
      $addFields: {
        variantsQuantity: {
          $reduce: {
            input: "$batches",
            initialValue: 0,
            in: {
              $sum: ["$$value", { $sum: "$$this.variants.quantity" }]
            }
          }
        },
        size: {
          $sum: {
            $map: {
              input: "$batches",
              as: "batch",
              in: { $size: "$$batch.variants" }
            }
          }
        },
      },
    },
    {
      $group: {
        _id: null,
        inactive: {
          $sum: {
            $cond: {
              if: { $eq: ["$status", constants.PRODUCT.STATUS.INACTIVE] },
              then: 1,
              else: 0,
            },
          },
        },
        active: {
          $sum: {
            $cond: {
              if: {
                $and: [
                  { $eq: ["$status", constants.PRODUCT.STATUS.ACTIVE] },
                  { $gt: ["$variantsQuantity", 0] },
                  {
                    $or: [
                      {
                        $and: [
                          { $eq: ["$size", 1] }
                        ],
                      },
                      {
                        $and: [
                          { $gt: ["$size", 1] }
                        ],
                      },
                    ],
                  },
                ],
              },
              then: 1,
              else: 0,
            },
          },
        },
        soldout: {
          $sum: {
            $cond: {
              if: {
                $and: [
                  { $eq: ["$status", constants.PRODUCT.STATUS.ACTIVE] },
                  { $eq: ["$variantsQuantity", 0] },
                  {
                    $or: [
                      {
                        $and: [
                          { $eq: ["$size", 1] },
                        ],
                      },
                      {
                        $and: [
                          { $gt: ["$size", 1] },
                        ],
                      },
                    ],
                  },
                ],
              },
              then: 1,
              else: 0,
            },
          },
        },
      },
    },
    {
      $project: {
        _id: 0,
      },
    },
  ]).then((array) => {
    return array[0] || { inactive: 0, active: 0, soldout: 0 }
  });
}

function ticketStatistics(vendor) {
  const { Ticket } = mongoose.models;
  return Ticket.aggregate([
    {
      $match: {
        _vendor: vendor._id,
      },
    },
    {
      $group: {
        _id: null,
        opened: {
          $sum: {
            $cond: {
              if: { $eq: ["$status", constants.TICKET.STATUS.OPENED] },
              then: 1,
              else: 0,
            },
          },
        },
        closed: {
          $sum: {
            $cond: {
              if: { $eq: ["$status", constants.TICKET.STATUS.CLOSED] },
              then: 1,
              else: 0,
            },
          },
        },
      },
    },
    {
      $project: {
        _id: 0,
      },
    },
  ]).then((array) => array[0] || { opened: 0, closed: 0 });
}
