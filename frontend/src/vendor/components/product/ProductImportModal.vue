<template>
  <div class="modal-dialog modal-lg">
    <div class="modal-content">
      <div class="modal-header">
        <button type="button" class="close" @click="$emit('close')">
          <span aria-hidden="true">&times;</span>
        </button>
        <h4 class="modal-title">Import Products</h4>
      </div>
      <div class="modal-body">
        <div v-if="step === 'upload'">
          <div class="alert alert-info mb-4">
            <div class="d-flex">
              <div class="mr-3">
                <svg class="icon icon-lg" viewBox="0 0 24 24">
                  <path d="M11 17H13V11H11V17ZM12 2C6.48 2 2 6.48 2 12S6.48 22 12 22 22 17.52 22 12 17.52 2 12 2ZM12 20C7.59 20 4 16.41 4 12S7.59 4 12 4 20 7.59 20 12 16.41 20 12 20ZM11 9H13V7H11V9Z" fill-rule="evenodd"></path>
                </svg>
              </div>
              <div>
                <p>Upload a spreadsheet with your product information. The file should be in CSV or Excel format.</p>
                <p class="mb-0">
                  <a href="#" @click.prevent="downloadTemplate" class="btn btn-sm btn-outline-primary">
                    <svg class="icon mr-1" viewBox="0 0 24 24">
                      <path d="M19 9h-4V3H9v6H5l7 7 7-7zM5 18v2h14v-2H5z" fill-rule="evenodd"></path>
                    </svg>
                    Download Template
                  </a>
                </p>
              </div>
            </div>
          </div>

          <div class="text-center p-5 border border-dashed rounded mb-4">
            <div v-if="!file">
              <label class="btn btn-primary mb-3">
                Select Spreadsheet File
                <input
                  type="file"
                  accept=".csv,.xlsx,.xls"
                  @change="onFileSelected"
                  class="d-none"
                />
              </label>
              <p class="text-muted">Supported formats: .csv, .xlsx, .xls</p>
              <p class="text-muted">Please use the template file for best results</p>
            </div>
            <div v-else>
              <div class="d-flex align-items-center justify-content-center">
                <div class="mr-3">
                  <svg class="icon icon-2x" viewBox="0 0 24 24">
                    <path d="M14 2H6c-1.1 0-1.99.9-1.99 2L4 20c0 1.1.89 2 1.99 2H18c1.1 0 2-.9 2-2V8l-6-6zm2 16H8v-2h8v2zm0-4H8v-2h8v2zm-3-5V3.5L18.5 9H13z" fill-rule="evenodd"></path>
                  </svg>
                </div>
                <div class="flex-grow-1 text-left">
                  <div>{{ file.name }}</div>
                  <div class="text-muted">{{ formatFileSize(file.size) }}</div>
                </div>
                <div>
                  <button type="button" class="btn btn-sm btn-outline-danger" @click="removeFile">
                    <svg class="icon" viewBox="0 0 24 24">
                      <path d="M19 6.41L17.59 5 12 10.59 6.41 5 5 6.41 10.59 12 5 17.59 6.41 19 12 13.41 17.59 19 19 17.59 13.41 12z" fill-rule="evenodd"></path>
                    </svg>
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>

        <div v-if="step === 'validate'" class="text-center">
          <div v-if="isValidating">
            <div class="spinner-border text-primary mb-3" role="status">
              <span class="sr-only">Validating...</span>
            </div>
            <p>Validating your spreadsheet...</p>
          </div>
          <div v-else-if="needsFieldMapping">
            <field-mapping-modal
              :file-data="fileData"
              @close="step = 'upload'"
              @mapping-applied="applyFieldMapping"
            />
          </div>
          <div v-else-if="validationErrors.length">
            <div class="alert alert-danger">
              <h5>Validation Errors</h5>
              <ul class="text-left mb-0">
                <li v-for="(error, index) in validationErrors" :key="index">{{ error }}</li>
              </ul>
            </div>
          </div>
          <div v-else>
            <div class="alert alert-success">
              <svg class="icon icon-lg mr-2" viewBox="0 0 24 24">
                <path d="M9 16.17L4.83 12l-1.42 1.41L9 19 21 7l-1.41-1.41z" fill-rule="evenodd"></path>
              </svg>
              Validation successful!
            </div>
            <div class="text-left">
              <h5>Summary:</h5>
              <ul>
                <li>New products to create: {{ summary.newProducts }}</li>
                <li>Existing products to update: {{ summary.updateProducts }}</li>
                <li>Total variants: {{ summary.totalVariants }}</li>
              </ul>
            </div>
          </div>
        </div>

        <div v-if="step === 'import'" class="text-center">
          <div v-if="isImporting">
            <div class="spinner-border text-primary mb-3" role="status">
              <span class="sr-only">Importing...</span>
            </div>
            <p>Importing your products...</p>
            <div class="progress">
              <div class="progress-bar" :style="{width: importProgress + '%'}"></div>
            </div>
            <p>{{ importedCount }} of {{ totalToImport }} products processed</p>
          </div>
          <div v-else-if="importErrors.length">
            <div class="alert alert-warning">
              <h5>Import Completed with Warnings</h5>
              <p>{{ successCount }} products were imported successfully.</p>
              <p>{{ importErrors.length }} products had errors:</p>
              <ul class="text-left">
                <li v-for="(error, index) in importErrors" :key="index">{{ error }}</li>
              </ul>
            </div>
          </div>
          <div v-else>
            <div class="alert alert-success">
              <svg class="icon icon-lg mr-2" viewBox="0 0 24 24">
                <path d="M9 16.17L4.83 12l-1.42 1.41L9 19 21 7l-1.41-1.41z" fill-rule="evenodd"></path>
              </svg>
              Import completed successfully!
            </div>
            <p>{{ successCount }} products were imported.</p>
          </div>
        </div>
      </div>
      <div class="modal-footer">
        <button type="button" class="btn btn-default" @click="$emit('close')">
          {{ step === 'import' && !isImporting ? 'Close' : 'Cancel' }}
        </button>
        <button
          v-if="step === 'upload'"
          type="button"
          class="btn btn-primary"
          :disabled="!file"
          @click="validateFile"
        >
          Validate
        </button>
        <button
          v-if="step === 'validate' && !isValidating && !validationErrors.length"
          type="button"
          class="btn btn-success"
          @click="importProducts"
        >
          Import Products
        </button>
        <button
          v-if="step === 'validate' && validationErrors.length"
          type="button"
          class="btn btn-primary"
          @click="step = 'upload'"
        >
          Back to Upload
        </button>
      </div>
    </div>
  </div>
</template>

<script>
import { saveAs } from 'file-saver';
import FieldMappingModal from './FieldMappingModal.vue';

export default {
  name: 'ProductImportModal',
  components: {
    FieldMappingModal
  },
  inject: ['$productService'],
  data() {
    return {
      step: 'upload', // upload, validate, import
      file: null,
      isValidating: false,
      isImporting: false,
      validationErrors: [],
      importErrors: [],
      importProgress: 0,
      importedCount: 0,
      totalToImport: 0,
      successCount: 0,
      needsFieldMapping: false,
      fieldMapping: null,
      fileData: null,
      summary: {
        newProducts: 0,
        updateProducts: 0,
        totalVariants: 0
      }
    };
  },
  methods: {
    onFileSelected(event) {
      const file = event.target.files[0];
      if (!file) return;

      // Check if it's a valid file type
      const validTypes = [
        'text/csv',
        'application/vnd.ms-excel',
        'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
      ];

      // Some browsers might return a generic mime type
      const validExtensions = ['.csv', '.xlsx', '.xls'];
      const fileExtension = file.name.substring(file.name.lastIndexOf('.')).toLowerCase();

      if (!validTypes.includes(file.type) && !validExtensions.includes(fileExtension)) {
        this.$flashError('Please upload a CSV or Excel file.');
        return;
      }

      this.file = file;
    },
    removeFile() {
      this.file = null;
    },
    formatFileSize(bytes) {
      if (bytes === 0) return '0 Bytes';
      const k = 1024;
      const sizes = ['Bytes', 'KB', 'MB', 'GB'];
      const i = Math.floor(Math.log(bytes) / Math.log(k));
      return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    },
    downloadTemplate(format = 'xlsx') {
      this.$productService.getImportTemplate(format)
        .then(response => {
          const blob = new Blob([response], { type: response.type });
          const filename = `product_import_template.${format}`;
          saveAs(blob, filename);
        })
        .catch(error => {
          this.$flashError('Error downloading template: ' + error.message);
        });
    },
    validateFile() {
      this.step = 'validate';
      this.isValidating = true;
      this.validationErrors = [];
      this.needsFieldMapping = false;

      // Use the file directly from the file input
      const fileToUpload = this.file;

      // Create a FormData object to send the file
      const formData = new FormData();
      formData.append('file', fileToUpload);

      this.$productService.validateImportFile(fileToUpload)
        .then(response => {
          this.isValidating = false;

          if (response.data && response.data.data) {
            const { valid, errors, summary, needsMapping, sheets, isTemplateValid } = response.data.data;

            // Check if the template is valid
            if (isTemplateValid === false) {
              this.validationErrors = ['The file does not match the expected template format. Please download and use the template file.'];
              return;
            }

            if (needsMapping) {
              this.needsFieldMapping = true;
              this.fileData = { sheets };
            } else if (valid) {
              this.summary = summary;
            } else {
              this.validationErrors = errors || ['Unknown validation error'];
            }
          } else {
            this.validationErrors = ['Invalid response from server'];
          }
        })
        .catch(error => {
          this.isValidating = false;
          this.validationErrors = [error.message || 'Error validating file'];
        });
    },

    applyFieldMapping(mapping) {
      this.fieldMapping = mapping;
      this.needsFieldMapping = false;
      this.isValidating = true;

      // Use the file directly from the file input
      const fileToUpload = this.file;

      // Create form data with the file and mapping
      const formData = new FormData();
      formData.append('file', fileToUpload);
      formData.append('fieldMapping', JSON.stringify(this.fieldMapping));

      // Validate again with the mapping
      this.$productService.validateImportFile(fileToUpload, this.fieldMapping)
        .then(response => {
          this.isValidating = false;

          if (response.data && response.data.data) {
            const { valid, errors, summary, isTemplateValid } = response.data.data;

            // Check if the template is valid
            if (isTemplateValid === false) {
              this.validationErrors = ['The file does not match the expected template format. Please download and use the template file.'];
              return;
            }

            if (valid) {
              this.summary = summary;
            } else {
              this.validationErrors = errors || ['Unknown validation error'];
            }
          } else {
            this.validationErrors = ['Invalid response from server'];
          }
        })
        .catch(error => {
          this.isValidating = false;
          this.validationErrors = [error.message || 'Error validating file'];
        });
    },
    importProducts() {
      this.step = 'import';
      this.isImporting = true;
      this.importErrors = [];
      this.importProgress = 0;
      this.importedCount = 0;
      this.totalToImport = this.summary.newProducts + this.summary.updateProducts;

      // Use the file directly from the file input
      const fileToUpload = this.file;

      // Create form data with the file and mapping if available
      const formData = new FormData();
      formData.append('file', fileToUpload);

      if (this.fieldMapping) {
        formData.append('fieldMapping', JSON.stringify(this.fieldMapping));
      }

      this.$productService.importProducts(fileToUpload, this.fieldMapping)
        .then(response => {
          this.isImporting = false;

          if (response.data && response.data.data) {
            const { success, errors, warnings } = response.data.data;

            this.successCount = success ? success.length : 0;

            // Process errors
            this.importErrors = [];
            if (errors && errors.length > 0) {
              this.importErrors = errors.map(err => `Product ${err.sku}: ${err.error}`);
            }

            // Process warnings
            if (warnings && warnings.length > 0) {
              warnings.forEach(warning => {
                this.importErrors.push(`Warning - Product ${warning.sku}: ${warning.warning}`);
              });
            }

            // Set progress to 100%
            this.importProgress = 100;
            this.importedCount = this.totalToImport;

            // Refresh the product list
            this.$emit('import-complete');
          } else {
            this.importErrors = ['Invalid response from server'];
          }
        })
        .catch(error => {
          this.isImporting = false;
          this.importErrors = [error.message || 'Error importing products'];
        });
    }
  }
};
</script>
