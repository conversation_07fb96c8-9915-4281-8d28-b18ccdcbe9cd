'use strict';

const Router = require('koa-router');
const exporter = require('../../services/exporter/index');

module.exports = Router()
    .prefix('/tools')
    .post('/export', postExport)
    .post('/export/special', postExportSpecial);


async function postExport(ctx) {
    const {format, collection, filters} = ctx.request.body;

    if (!['csv', 'xlsx'].includes(format)) {
        ctx.throw(422, 'Wrong format');
    }

    const {stream, mime} = await exporter.exportTo(format, collection, filters);
    ctx.set('Content-Type', mime);
    ctx.body = stream;
}

async function postExportSpecial(ctx) {
    const {format, collection, filters} = ctx.request.body;

    if (!['csv', 'xlsx'].includes(format)) {
        ctx.throw(422, 'Wrong format');
    }

    const {stream, mime} = await exporter.specialExportTo(format, collection, filters);
    ctx.set('Content-Type', mime);
    ctx.body = stream;
}
