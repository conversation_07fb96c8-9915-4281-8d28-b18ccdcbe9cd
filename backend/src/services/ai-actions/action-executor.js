'use strict';

const { AppError } = require('../error');
const constants = require('../../models/constants');

// Registry of available actions
const actionRegistry = new Map();

// Register an action handler
function registerAction(actionName, handler, requiredPermissions = []) {
    actionRegistry.set(actionName, {
        handler,
        requiredPermissions,
        name: actionName
    });
}

// Execute an action with proper validation and error handling
async function executeAction(actionName, params, context) {
    const action = actionRegistry.get(actionName);
    
    if (!action) {
        throw new AppError(400, `Unknown action: ${actionName}`);
    }
    
    // Validate permissions
    if (action.requiredPermissions.length > 0) {
        const hasPermission = action.requiredPermissions.some(permission => 
            context.userPermissions?.includes(permission)
        );
        
        if (!hasPermission) {
            throw new AppError(403, `Insufficient permissions for action: ${actionName}`);
        }
    }
    
    // Validate required parameters
    const validationResult = validateActionParams(actionName, params);
    if (!validationResult.valid) {
        throw new AppError(422, `Invalid parameters: ${validationResult.error}`);
    }
    
    try {
        // Execute the action with audit logging
        const result = await action.handler(params, context);
        
        // Log the action for audit purposes
        await logActionExecution(actionName, params, context, result);
        
        return {
            success: true,
            action: actionName,
            result,
            message: `Successfully executed ${actionName}`
        };
    } catch (error) {
        console.error(`Action execution failed for ${actionName}:`, error);
        
        // Log the failure
        await logActionExecution(actionName, params, context, null, error);
        
        throw new AppError(500, `Failed to execute ${actionName}: ${error.message}`);
    }
}

// Validate action parameters
function validateActionParams(actionName, params) {
    const validationRules = {
        'change_password': {
            required: ['currentPassword', 'newPassword'],
            types: { currentPassword: 'string', newPassword: 'string' }
        },
        'update_profile': {
            required: ['field', 'value'],
            types: { field: 'string', value: 'string' }
        },
        'create_ticket': {
            required: ['subject', 'message'],
            types: { subject: 'string', message: 'string' }
        },
        'search_products': {
            required: ['query'],
            types: { query: 'string' }
        }
    };
    
    const rules = validationRules[actionName];
    if (!rules) {
        return { valid: true }; // No specific validation rules
    }
    
    // Check required parameters
    for (const required of rules.required) {
        if (!(required in params)) {
            return { valid: false, error: `Missing required parameter: ${required}` };
        }
    }
    
    // Check parameter types
    for (const [param, expectedType] of Object.entries(rules.types)) {
        if (params[param] && typeof params[param] !== expectedType) {
            return { valid: false, error: `Parameter ${param} must be of type ${expectedType}` };
        }
    }
    
    return { valid: true };
}

// Get available actions for a user context
function getAvailableActions(context) {
    const availableActions = [];
    
    for (const [actionName, action] of actionRegistry.entries()) {
        // Check if user has required permissions
        if (action.requiredPermissions.length === 0 || 
            action.requiredPermissions.some(permission => 
                context.userPermissions?.includes(permission)
            )) {
            availableActions.push({
                name: actionName,
                description: getActionDescription(actionName),
                requiredPermissions: action.requiredPermissions
            });
        }
    }
    
    return availableActions;
}

// Get human-readable description for an action
function getActionDescription(actionName) {
    const descriptions = {
        'change_password': 'Change user password',
        'update_profile': 'Update user profile information',
        'create_ticket': 'Create a support ticket',
        'search_products': 'Search for products in the marketplace',
        'get_order_status': 'Check the status of an order',
        'update_notification_settings': 'Update email notification preferences',
        'get_account_info': 'Retrieve account information',
        'reset_password': 'Reset user password via email'
    };
    
    return descriptions[actionName] || `Execute ${actionName}`;
}

// Log action execution for audit purposes
async function logActionExecution(actionName, params, context, result, error = null) {
    try {
        const logEntry = {
            timestamp: new Date(),
            action: actionName,
            userId: context.userId,
            userRole: context.userRole,
            success: !error,
            error: error?.message || null,
            // Don't log sensitive parameters like passwords
            sanitizedParams: sanitizeParams(actionName, params)
        };
        
        // This would typically go to a database or logging service
        console.log('AI Action Execution Log:', logEntry);
        
        // TODO: Implement proper audit logging to database
        // await AuditLog.create(logEntry);
        
    } catch (logError) {
        console.error('Failed to log action execution:', logError);
        // Don't throw - logging failures shouldn't break the main functionality
    }
}

// Remove sensitive information from parameters before logging
function sanitizeParams(actionName, params) {
    const sensitiveFields = ['password', 'currentPassword', 'newPassword', 'token', 'apiKey'];
    const sanitized = { ...params };
    
    for (const field of sensitiveFields) {
        if (sanitized[field]) {
            sanitized[field] = '[REDACTED]';
        }
    }
    
    return sanitized;
}

// Check if an action is safe to execute (doesn't modify critical data)
function isActionSafe(actionName) {
    const safeActions = [
        'search_products',
        'get_order_status',
        'get_account_info',
        'create_ticket'
    ];
    
    return safeActions.includes(actionName);
}

// Check if an action requires user confirmation
function requiresConfirmation(actionName) {
    const confirmationRequired = [
        'change_password',
        'update_profile',
        'delete_product',
        'cancel_order'
    ];
    
    return confirmationRequired.includes(actionName);
}

module.exports = {
    registerAction,
    executeAction,
    getAvailableActions,
    getActionDescription,
    isActionSafe,
    requiresConfirmation,
    validateActionParams
};
