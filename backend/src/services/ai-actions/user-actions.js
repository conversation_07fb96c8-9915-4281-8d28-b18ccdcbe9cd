'use strict';

const mongoose = require('mongoose');
const bcrypt = require('bcrypt');
const { AppError } = require('../error');
const { registerAction } = require('./action-executor');

// Initialize user-related actions
function initUserActions() {
    // Register all user-related actions
    registerAction('change_password', changePassword, ['update_profile']);
    registerAction('update_profile', updateProfile, ['update_profile']);
    registerAction('get_account_info', getAccountInfo, []);
    registerAction('update_notification_settings', updateNotificationSettings, ['update_profile']);
    registerAction('create_ticket', createTicket, ['create_tickets']);
}

// Change user password
async function changePassword(params, context) {
    const { currentPassword, newPassword } = params;
    const { User } = mongoose.models;
    
    if (!context.userId) {
        throw new AppError(401, 'User not authenticated');
    }
    
    // Validate new password strength
    if (newPassword.length < 8) {
        throw new AppError(422, 'New password must be at least 8 characters long');
    }
    
    // Get user from database
    const user = await User.findById(context.userId);
    if (!user) {
        throw new AppError(404, 'User not found');
    }
    
    // Verify current password
    const isCurrentPasswordValid = await bcrypt.compare(currentPassword, user.password);
    if (!isCurrentPasswordValid) {
        throw new AppError(422, 'Current password is incorrect');
    }
    
    // Hash new password
    const saltRounds = 12;
    const hashedNewPassword = await bcrypt.hash(newPassword, saltRounds);
    
    // Update password
    await User.findByIdAndUpdate(context.userId, {
        password: hashedNewPassword,
        updatedAt: new Date()
    });
    
    return {
        message: 'Password changed successfully',
        timestamp: new Date()
    };
}

// Update user profile information
async function updateProfile(params, context) {
    const { field, value } = params;
    const { User } = mongoose.models;
    
    if (!context.userId) {
        throw new AppError(401, 'User not authenticated');
    }
    
    // Define allowed fields that can be updated
    const allowedFields = {
        'name': { type: 'string', maxLength: 100 },
        'phone': { type: 'string', maxLength: 20 },
        'email': { type: 'string', maxLength: 255 }
    };
    
    if (!allowedFields[field]) {
        throw new AppError(422, `Field '${field}' cannot be updated`);
    }
    
    // Validate field value
    const fieldConfig = allowedFields[field];
    if (typeof value !== fieldConfig.type) {
        throw new AppError(422, `Field '${field}' must be of type ${fieldConfig.type}`);
    }
    
    if (value.length > fieldConfig.maxLength) {
        throw new AppError(422, `Field '${field}' exceeds maximum length of ${fieldConfig.maxLength}`);
    }
    
    // Special validation for email
    if (field === 'email') {
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        if (!emailRegex.test(value)) {
            throw new AppError(422, 'Invalid email format');
        }
        
        // Check if email is already in use
        const existingUser = await User.findOne({ 
            email: value, 
            _id: { $ne: context.userId } 
        });
        if (existingUser) {
            throw new AppError(422, 'Email is already in use');
        }
    }
    
    // Update the field
    const updateData = {
        [field]: value,
        updatedAt: new Date()
    };
    
    await User.findByIdAndUpdate(context.userId, updateData);
    
    return {
        message: `${field} updated successfully`,
        field,
        newValue: value,
        timestamp: new Date()
    };
}

// Get user account information
async function getAccountInfo(params, context) {
    const { User } = mongoose.models;
    
    if (!context.userId) {
        throw new AppError(401, 'User not authenticated');
    }
    
    const user = await User.findById(context.userId)
        .select('name email phone createdAt updatedAt')
        .lean();
    
    if (!user) {
        throw new AppError(404, 'User not found');
    }
    
    // Add role-specific information
    let roleInfo = {};
    if (context.userRole === 'vendor' && context.vendorInfo) {
        roleInfo = {
            role: 'vendor',
            storeName: context.vendorInfo.storeName,
            enabled: context.vendorInfo.enabled,
            hasApplication: context.vendorInfo.hasApplication
        };
    } else if (context.userRole === 'retailer' && context.retailerInfo) {
        roleInfo = {
            role: 'retailer',
            businessName: context.retailerInfo.businessName,
            enabled: context.retailerInfo.enabled,
            hasApplication: context.retailerInfo.hasApplication
        };
    }
    
    return {
        user: {
            ...user,
            ...roleInfo
        },
        timestamp: new Date()
    };
}

// Update notification settings
async function updateNotificationSettings(params, context) {
    const { setting, enabled } = params;
    const { User } = mongoose.models;
    
    if (!context.userId) {
        throw new AppError(401, 'User not authenticated');
    }
    
    const allowedSettings = [
        'emailNotifications',
        'orderUpdates',
        'marketingEmails',
        'ticketNotifications'
    ];
    
    if (!allowedSettings.includes(setting)) {
        throw new AppError(422, `Invalid notification setting: ${setting}`);
    }
    
    if (typeof enabled !== 'boolean') {
        throw new AppError(422, 'Enabled must be a boolean value');
    }
    
    // Update notification settings
    const updateData = {
        [`notificationSettings.${setting}`]: enabled,
        updatedAt: new Date()
    };
    
    await User.findByIdAndUpdate(context.userId, updateData);
    
    return {
        message: `Notification setting '${setting}' ${enabled ? 'enabled' : 'disabled'}`,
        setting,
        enabled,
        timestamp: new Date()
    };
}

// Create a support ticket
async function createTicket(params, context) {
    const { subject, message, priority = 'medium' } = params;
    const { Ticket } = mongoose.models;
    
    if (!context.userId) {
        throw new AppError(401, 'User not authenticated');
    }
    
    // Validate parameters
    if (subject.length < 5 || subject.length > 200) {
        throw new AppError(422, 'Subject must be between 5 and 200 characters');
    }
    
    if (message.length < 10 || message.length > 2000) {
        throw new AppError(422, 'Message must be between 10 and 2000 characters');
    }
    
    const validPriorities = ['low', 'medium', 'high', 'urgent'];
    if (!validPriorities.includes(priority)) {
        throw new AppError(422, 'Invalid priority level');
    }
    
    // Create the ticket
    const ticketData = {
        subject,
        message,
        priority,
        status: 'open',
        createdBy: context.userId,
        createdAt: new Date(),
        updatedAt: new Date()
    };
    
    // Add role-specific information
    if (context.userRole === 'vendor' && context.vendorInfo) {
        ticketData.vendor = context.vendorInfo.vendorId;
    } else if (context.userRole === 'retailer' && context.retailerInfo) {
        ticketData.retailer = context.retailerInfo.retailerId;
    }
    
    const ticket = await Ticket.create(ticketData);
    
    return {
        message: 'Support ticket created successfully',
        ticketId: ticket._id,
        ticketNumber: ticket.ticketNumber || ticket._id.toString().slice(-8),
        subject,
        priority,
        timestamp: new Date()
    };
}

module.exports = {
    initUserActions
};
