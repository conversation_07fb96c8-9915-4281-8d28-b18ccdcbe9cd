const Router = require('koa-router');
const config = require('../../services/config');
const fs = require('fs');
const path = require('path');
const emailService = require('../../services/email');

module.exports = Router()
    .prefix('/settings')
    .get('/signup-notifications', getSignupNotifications)
    .put('/signup-notifications', updateSignupNotifications)
    .post('/signup-notifications/test', testSignupNotification);

async function getSignupNotifications(ctx) {
    try {
        const settings = {
            emails: config.signupNotificationEmails || [],
            enabled: Array.isArray(config.signupNotificationEmails) && config.signupNotificationEmails.length > 0
        };
        
        ctx.body = settings;
    } catch (error) {
        console.error('Error getting signup notification settings:', error);
        ctx.status = 500;
        ctx.body = { error: 'Failed to get signup notification settings' };
    }
}

async function updateSignupNotifications(ctx) {
    try {
        const { emails, enabled } = ctx.request.body;
        
        // Validate emails
        const errors = {};
        if (!Array.isArray(emails)) {
            errors.emails = ['Emails must be an array'];
        } else {
            const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
            const invalidEmails = emails.filter(email => !emailRegex.test(email));
            if (invalidEmails.length > 0) {
                errors.emails = [`Invalid email addresses: ${invalidEmails.join(', ')}`];
            }
        }
        
        if (Object.keys(errors).length > 0) {
            ctx.status = 400;
            ctx.body = { errors };
            return;
        }
        
        // Update config file
        const configPath = path.join(__dirname, '../../../../config/app.js');
        let configContent = fs.readFileSync(configPath, 'utf8');
        
        // Update the signupNotificationEmails array
        const emailsArray = enabled && emails.length > 0 ? emails : [];
        const emailsString = emailsArray.map(email => `        '${email}'`).join(',\n');
        
        const signupNotificationRegex = /signupNotificationEmails:\s*\[[^\]]*\]/s;
        const newSignupNotificationEmails = `signupNotificationEmails: [
        ${emailsString}
    ]`;
        
        if (signupNotificationRegex.test(configContent)) {
            configContent = configContent.replace(signupNotificationRegex, newSignupNotificationEmails);
        } else {
            // Add the setting if it doesn't exist
            const insertPoint = configContent.lastIndexOf('};');
            const beforeClosing = configContent.substring(0, insertPoint);
            const afterClosing = configContent.substring(insertPoint);
            
            configContent = beforeClosing + 
                (beforeClosing.trim().endsWith(',') ? '' : ',') + 
                '\n    \n    // Admin email addresses that should receive notifications for new user signups\n    ' + 
                newSignupNotificationEmails + '\n' + 
                afterClosing;
        }
        
        fs.writeFileSync(configPath, configContent);
        
        // Update the in-memory config
        config.signupNotificationEmails = emailsArray;
        
        ctx.body = { 
            success: true, 
            message: 'Signup notification settings updated successfully',
            settings: {
                emails: emailsArray,
                enabled: emailsArray.length > 0
            }
        };
    } catch (error) {
        console.error('Error updating signup notification settings:', error);
        ctx.status = 500;
        ctx.body = { error: 'Failed to update signup notification settings' };
    }
}

async function testSignupNotification(ctx) {
    try {
        if (!config.signupNotificationEmails || config.signupNotificationEmails.length === 0) {
            ctx.status = 400;
            ctx.body = { error: 'No email addresses configured for signup notifications' };
            return;
        }
        
        // Create a test user object
        const testUser = {
            _id: 'test-user-id',
            name: 'Test User',
            email: '<EMAIL>',
            phone: '******-123-4567',
            key: 'test-user-key',
            formatPublic: () => ({
                name: 'Test User',
                email: '<EMAIL>',
                phone: '******-123-4567'
            }),
            populate: () => ({ execPopulate: () => Promise.resolve() })
        };
        
        const testRetailer = {
            _id: 'test-retailer-id',
            enabled: true,
            businessName: 'Test Retail Business',
            retailerBudget: '$10,000 - $50,000',
            formatPublic: () => ({
                businessName: 'Test Retail Business',
                retailerBudget: '$10,000 - $50,000'
            })
        };
        
        const testVendor = {
            _id: 'test-vendor-id',
            enabled: true,
            storeName: 'Test Vendor Store',
            vendorProductCount: '50-100 products',
            isBroker: false,
            formatPublic: () => ({
                storeName: 'Test Vendor Store',
                vendorProductCount: '50-100 products',
                isBroker: false
            })
        };
        
        // Send test notification
        await emailService.create.adminNewSignupEmail({
            user: testUser,
            retailer: testRetailer,
            vendor: testVendor
        });
        
        ctx.body = { 
            success: true, 
            message: `Test email sent to ${config.signupNotificationEmails.length} recipient(s)` 
        };
    } catch (error) {
        console.error('Error sending test signup notification:', error);
        ctx.status = 500;
        ctx.body = { error: 'Failed to send test notification' };
    }
}


