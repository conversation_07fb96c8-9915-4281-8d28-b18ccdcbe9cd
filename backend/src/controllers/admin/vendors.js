'use strict';

const config = require('../../services/config');
const mongoose = require('mongoose');
const ObjectId = mongoose.Types.ObjectId;
const Bluebird = require('bluebird');
const constants = require('../../models/constants');
const { HttpError } = require('../../services/error');
const regexEscape = require('escape-string-regexp');
const mw = require('../../services/middleware');
const Router = require('koa-router');
const sentry = require('../../services/sentry');

const logger = sentry.logger('admin.controller.vendor');

module.exports = Router()
    .prefix('/vendors')
    .param('vendorKey', paramVendor)
    .get('/', getVendors)
    .get('/:vendorKey', getVendor)
    .patch('/:vendorKey', patchVendor);

async function paramVendor(vendorKey, ctx, next) {
    const { Vendor } = mongoose.models;
    const vendor = await Vendor.findOne({ key: vendorKey, deletedAt: { $exists: false } }).populate('team._user _retailer _application requiredDocuments');

    if (!vendor) {
        throw new HttpError(404, 'Vendor not found');
    }
    ctx.state.vendor = vendor;

    await next();
}

async function getVendors(ctx) {
    const { Vendor, Application } = mongoose.models;
    const payload = ctx.request.query;
    const query = { deletedAt: { $exists: false } };
    const sort = payload.sort || '-createdAt';

    if (payload.applicationStatus) {
        if (payload.applicationStatus === 'noSubmitted') {
            query['application.status'] = { $exists: false };
        }
        else {
            query['application.status'] = payload.applicationStatus;
        }
    }
    if (payload.ein) {
        const emptyEin = Application.emptyEin();
        if (payload.ein === 'provided') {
            query['application.ein'] = { $ne: emptyEin };
        }
        else if (payload.ein === 'not_provided') {
            query['application.ein'] = { $eq: emptyEin };
        }
    }

    if (payload.status) {
        query.status = payload.status;
    }
    payload.enabled = payload.enabled || 'true';
    query.enabled = payload.enabled === 'true';

    if (payload.q) {
        const regex = new RegExp(regexEscape(payload.q), 'i');
        query.$or = [
            { 'team.email': { $regex: regex } },
            { 'team.name': { $regex: regex } },
            { key: { $regex: regex } },
            { storeName: { $regex: regex } },
            { slug: { $regex: regex } }
        ];
    }
    if (ctx.state.currentState) {
        query._state = ctx.state.currentState._id;
    }

    if (payload.lastTermsDate) {
        if (payload.lastTermsDate === 'accepted') {
            query.lastAgreeTermsAt = { $exists: true };
        }
        else if (payload.lastTermsDate === 'notAccepted') {
            query.lastAgreeTermsAt = { $exists: false };
        }
        else if (payload.lastTermsDate === 'acceptedLast') {
            query.lastAgreeTermsAt = { $gt: config.lastTermsDate };
        }
        else if (payload.lastTermsDate === 'notAcceptedLast') {
            query.lastAgreeTermsAt = { $lt: config.lastTermsDate };
        }
    }

    if (payload.products) {
        switch (payload.products) {
            case 'zero':
                query.productsCount = 0;
                break;
            case 'betweenOneAndTen':
                query.productsCount = {
                    $gte: 1,
                    $lte: 10
                };
                break;
            case 'aboveTen':
                query.productsCount = { $gt: 10 };
                break;
            case 'allActive':
                query.productsCount = { $gt: 0 };
                query.allActiveProducts = true;
                break;
            case 'allInactive':
                query.productsCount = { $gt: 0 };
                query.allInactiveProducts = true;
                break;
            case 'aboveHalfHaveImages':
                query.productsCount = { $gt: 0 };
                query.aboveHalfHaveImages = true;
                break;
            case 'lessHalfHaveImages':
                query.productsCount = { $gt: 0 };
                query.aboveHalfHaveImages = false;
                break;
            default:
        }
    }

    const { list, count, total } = await Bluebird.props({
        list: adminListVendors(query, sort)
            .skip((payload.page - 1) * payload.perPage)
            .limit(payload.perPage)
            .exec(),

        count: adminListVendors(query, sort).count('count'),
        total: Vendor.count({})
    });
    ctx.body = {
        data: list,
        pagination: {
            count: count[0] && count[0].count || 0,
            total,
            page: payload.page,
            perPage: payload.perPage
        },
        filters: { ...payload }
    };
}
async function getVendor(ctx) {

    await ctx.state.vendor.populate('_products').execPopulate();
    const products = ctx.state.vendor._products;
    const formattedVendor = ctx.state.vendor.formatAdmin();
    formattedVendor.activeProductsCount = 0;
    formattedVendor.inactiveProductsCount = 0;
    formattedVendor.productImagesCount = 0;
    formattedVendor.productWithoutImagesCount = 0;
    formattedVendor.productWithoutImagesPercent = 0;
    products.forEach(product => {
        if (product.status === constants.PRODUCT.STATUS.ACTIVE) {
            formattedVendor.activeProductsCount++;
        }
        else if (product.status === constants.PRODUCT.STATUS.INACTIVE) {
            formattedVendor.inactiveProductsCount++;
        }
        
        const imagesCount = product.batches.reduce((acc, batch) => acc.concat(batch.variants), []).length;
        
        if (imagesCount) {
            formattedVendor.productImagesCount++;
        }
        else {
            formattedVendor.productWithoutImagesCount++;
        }
    });
    if (products.length) {
        formattedVendor.productWithoutImagesPercent = Math.trunc(formattedVendor.productWithoutImagesCount / products.length * 100);
    }

    ctx.body = {
        data: {
            vendor: formattedVendor
        }
    };
}
async function patchVendor(ctx) {
    const { SystemLog } = mongoose.models;
    const { Product } = mongoose.models;
    const { isBroker, returnProbability, revenueShare, adminNotes, enabled, showThc } = ctx.request.body;
    const { vendor } = ctx.state;

    const snapshotVendor = SystemLog.takeSnapshot(vendor);

    if ('isBroker' in ctx.request.body && isBroker !== vendor.isBroker) {
        vendor.set({
            isBroker,
            processedAt: new Date()
        });
    }
    
    if (revenueShare) {
        vendor.set({
            revenueShare,
            processedAt: new Date()
        });
    }
    if (adminNotes) {
        vendor.set({
            adminNotes,
            processedAt: new Date()
        });
    }
    if (returnProbability) {
        vendor.set({
            returnProbability,
            processedAt: new Date()
        });
    }
    if ('showThc' in ctx.request.body && showThc !== vendor.showThc) {
        vendor.set({ 
            showThc,
            processedAt: new Date()
        });
    }

    if ('enabled' in ctx.request.body) {
        vendor.set({ 
            enabled,
            processedAt: new Date()
        });
    }

    await vendor.save();
    SystemLog.adminVendorUpdated(ctx, snapshotVendor, vendor);
    Promise.resolve()
        .then(() => Product.find({ _vendor: vendor._id }))
        .then(productList => Promise.all(productList.map(product => product.save({ timestamps: false }))))
        .catch(err => {
            logger(err);
        });

    ctx.body = {
        data: vendor.formatAdmin()
    };
}

function adminListVendors(query, sort) {
    const { Vendor } = mongoose.models;
    sort = {
        [sort.split('-').reverse()[0]]: sort.startsWith('-') ? -1 : 1
    };

    return Vendor.aggregate([
        {
            $lookup:
            {
                from: 'applications',
                localField: '_application',
                foreignField: '_id',
                as: 'application'
            }
        },
        {
            $addFields: {
                application: { $arrayElemAt: ['$application', 0] }
            }
        },
        {
            $lookup:
            {
                from: 'products',
                localField: '_id',
                foreignField: '_vendor',
                as: 'products'
            }
        },
        {
            $addFields: {
                productsCount: { $size: '$products' },
                statuses: {
                    $map: {
                        input: '$products',
                        as: 'product',
                        in: { $eq: ['$$product.status', 'Active'] }
                    }
                },
                hasImages: {
                    $map: {
                        input: {
                            $map: {
                                input: '$products',
                                as: 'product',
                                in: {
                                    $filter: {
                                        input: {
                                            $concatArrays: ['$$product.images', '$$product.batches.variants.mainImage']
                                        },
                                        as: 'image',
                                        cond: '$$image.uuid'
                                    }
                                }
                            }
                        },
                        as: 'images',
                        in: {
                            $cond: {
                                if: { $eq: [{ $size: '$$images' }, 0] },
                                then: 0,
                                else: 1
                            }
                        }
                    }
                }

            }
        },
        {
            $addFields: {
                allActiveProducts: { $allElementsTrue: '$statuses' },
                allInactiveProducts: {
                    $allElementsTrue: {
                        $map: {
                            input: '$statuses',
                            as: 'status',
                            in: { $not: '$$status' }
                        }
                    }
                },
                aboveHalfHaveImages: {
                    $cond: {
                        if: { $gt: ['$productsCount', 0] },
                        then: {
                            $cond: {
                                if: { $lte: [{ $divide: [{ $sum: '$hasImages' }, '$productsCount'] }, 0.5] },
                                then: false,
                                else: true
                            }
                        },
                        else: null
                    }
                }
            }
        },
        {
            $match: query
        },
        {
            $sort: sort
        },
        {
            $project: {
                _id: 0,
                key: '$key',
                storeName: 1,
                slug: 1,
                application: {
                    status: '$application.status'
                },
                state: '$_state',
                lastAgreeTermsAt: 1,
                createdAt: 1
            }
        }
    ]);
}
