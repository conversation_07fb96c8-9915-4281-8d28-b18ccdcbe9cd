'use strict';

const { HttpError } = require('../services/error');

// Enhanced error handling middleware for AI system
async function aiSystemErrorHandler(ctx, next) {
    try {
        await next();
    } catch (error) {
        console.error('AI System Error:', {
            path: ctx.path,
            method: ctx.method,
            userId: ctx.state.user?._id,
            error: error.message,
            stack: error.stack
        });
        
        // Handle specific AI system errors
        if (error instanceof HttpError) {
            ctx.status = error.status;
            ctx.body = {
                error: error.message,
                code: error.status
            };
        } else if (error.name === 'ValidationError') {
            ctx.status = 422;
            ctx.body = {
                error: 'Validation failed',
                details: error.message,
                code: 422
            };
        } else if (error.code === 'ENOTFOUND' || error.code === 'ECONNREFUSED') {
            ctx.status = 503;
            ctx.body = {
                error: 'External service unavailable',
                code: 503
            };
        } else if (error.name === 'TimeoutError' || error.code === 'ECONNABORTED') {
            ctx.status = 408;
            ctx.body = {
                error: 'Request timeout',
                code: 408
            };
        } else if (error.message && error.message.includes('rate limit')) {
            ctx.status = 429;
            ctx.body = {
                error: 'Rate limit exceeded',
                code: 429
            };
        } else {
            // Generic server error
            ctx.status = 500;
            ctx.body = {
                error: 'Internal server error',
                code: 500
            };
        }
    }
}

// Input validation middleware
function validateInput(schema) {
    return async (ctx, next) => {
        try {
            const data = ctx.request.body || {};
            
            // Basic validation based on schema
            for (const [field, rules] of Object.entries(schema)) {
                const value = data[field];
                
                // Required field check
                if (rules.required && (value === undefined || value === null)) {
                    throw new HttpError(422, `${field} is required`);
                }
                
                // Type validation
                if (value !== undefined && rules.type && typeof value !== rules.type) {
                    throw new HttpError(422, `${field} must be of type ${rules.type}`);
                }
                
                // String length validation
                if (rules.type === 'string' && value) {
                    if (rules.minLength && value.length < rules.minLength) {
                        throw new HttpError(422, `${field} must be at least ${rules.minLength} characters`);
                    }
                    if (rules.maxLength && value.length > rules.maxLength) {
                        throw new HttpError(422, `${field} must be no more than ${rules.maxLength} characters`);
                    }
                }
                
                // Number range validation
                if (rules.type === 'number' && value !== undefined) {
                    if (rules.min !== undefined && value < rules.min) {
                        throw new HttpError(422, `${field} must be at least ${rules.min}`);
                    }
                    if (rules.max !== undefined && value > rules.max) {
                        throw new HttpError(422, `${field} must be no more than ${rules.max}`);
                    }
                }
                
                // Array validation
                if (rules.type === 'array' && value) {
                    if (!Array.isArray(value)) {
                        throw new HttpError(422, `${field} must be an array`);
                    }
                    if (rules.maxItems && value.length > rules.maxItems) {
                        throw new HttpError(422, `${field} can have at most ${rules.maxItems} items`);
                    }
                }
                
                // Custom validation
                if (rules.validate && typeof rules.validate === 'function') {
                    const isValid = rules.validate(value);
                    if (!isValid) {
                        throw new HttpError(422, `${field} is invalid`);
                    }
                }
            }
            
            await next();
        } catch (error) {
            if (error instanceof HttpError) {
                throw error;
            }
            throw new HttpError(422, 'Input validation failed');
        }
    };
}

// Rate limiting middleware for AI endpoints
function createRateLimiter(options = {}) {
    const {
        windowMs = 60000, // 1 minute
        maxRequests = 60,
        keyGenerator = (ctx) => ctx.request.ip
    } = options;
    
    const requests = new Map();
    
    return async (ctx, next) => {
        const key = keyGenerator(ctx);
        const now = Date.now();
        
        // Clean old requests
        if (requests.has(key)) {
            const userRequests = requests.get(key);
            const validRequests = userRequests.filter(time => now - time < windowMs);
            requests.set(key, validRequests);
        }
        
        const userRequests = requests.get(key) || [];
        
        if (userRequests.length >= maxRequests) {
            throw new HttpError(429, 'Rate limit exceeded');
        }
        
        userRequests.push(now);
        requests.set(key, userRequests);
        
        await next();
    };
}

// Sanitization middleware
function sanitizeInput() {
    return async (ctx, next) => {
        if (ctx.request.body && typeof ctx.request.body === 'object') {
            ctx.request.body = sanitizeObject(ctx.request.body);
        }
        
        await next();
    };
}

function sanitizeObject(obj, maxDepth = 5, currentDepth = 0) {
    if (currentDepth > maxDepth) {
        return '[Object too deep]';
    }
    
    if (obj === null || obj === undefined) {
        return obj;
    }
    
    if (typeof obj === 'string') {
        return obj
            .replace(/[<>]/g, '') // Remove HTML tags
            .replace(/javascript:/gi, '') // Remove javascript: protocol
            .replace(/on\w+=/gi, '') // Remove event handlers
            .trim()
            .substring(0, 10000); // Limit string length
    }
    
    if (typeof obj === 'number') {
        return isFinite(obj) ? obj : 0;
    }
    
    if (typeof obj === 'boolean') {
        return obj;
    }
    
    if (Array.isArray(obj)) {
        return obj.slice(0, 1000).map(item => sanitizeObject(item, maxDepth, currentDepth + 1));
    }
    
    if (typeof obj === 'object') {
        const sanitized = {};
        let count = 0;
        
        for (const [key, value] of Object.entries(obj)) {
            if (count >= 100) break; // Limit object properties
            
            const sanitizedKey = key
                .replace(/[<>]/g, '')
                .replace(/javascript:/gi, '')
                .trim()
                .substring(0, 100);
                
            sanitized[sanitizedKey] = sanitizeObject(value, maxDepth, currentDepth + 1);
            count++;
        }
        
        return sanitized;
    }
    
    return String(obj).substring(0, 1000);
}

// Authentication middleware for AI endpoints
function requireAuth() {
    return async (ctx, next) => {
        if (!ctx.state.user || !ctx.state.user._id) {
            throw new HttpError(401, 'Authentication required');
        }
        
        await next();
    };
}

// Role-based authorization middleware
function requireRole(allowedRoles) {
    return async (ctx, next) => {
        if (!ctx.state.user) {
            throw new HttpError(401, 'Authentication required');
        }
        
        const userRole = ctx.state.user.role || 'user';
        
        if (!allowedRoles.includes(userRole)) {
            throw new HttpError(403, 'Insufficient permissions');
        }
        
        await next();
    };
}

// Request logging middleware for AI endpoints
function logAIRequests() {
    return async (ctx, next) => {
        const startTime = Date.now();
        
        console.log('AI Request:', {
            method: ctx.method,
            path: ctx.path,
            userId: ctx.state.user?._id,
            userAgent: ctx.request.headers['user-agent'],
            ip: ctx.request.ip
        });
        
        try {
            await next();
        } finally {
            const duration = Date.now() - startTime;
            
            console.log('AI Response:', {
                method: ctx.method,
                path: ctx.path,
                status: ctx.status,
                duration: `${duration}ms`,
                userId: ctx.state.user?._id
            });
        }
    };
}

// Timeout middleware for AI requests
function timeoutMiddleware(timeoutMs = 30000) {
    return async (ctx, next) => {
        const timeout = new Promise((_, reject) => {
            setTimeout(() => reject(new Error('Request timeout')), timeoutMs);
        });
        
        try {
            await Promise.race([next(), timeout]);
        } catch (error) {
            if (error.message === 'Request timeout') {
                throw new HttpError(408, 'Request timeout');
            }
            throw error;
        }
    };
}

module.exports = {
    aiSystemErrorHandler,
    validateInput,
    createRateLimiter,
    sanitizeInput,
    requireAuth,
    requireRole,
    logAIRequests,
    timeoutMiddleware
};
