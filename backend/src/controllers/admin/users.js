'use strict';

const config = require('../../services/config');
const mongoose = require('mongoose');
const ObjectId = mongoose.Types.ObjectId;
const Bluebird = require('bluebird');
const constants = require('../../models/constants');
const {HttpError} = require('../../services/error');
const regexEscape = require('escape-string-regexp');
const mw = require('../../services/middleware');
const Router = require('koa-router');

module.exports = Router()
    .prefix('/users')
    .param('userKey', paramUser)
    .get('/', getUsers)
    .get('/:userKey', getUser)
    .delete('/:userKey', deleteUser)
    .patch('/:userKey', patchUser);


async function paramUser(userKey, ctx, next) {
    const {User, Retailer} = mongoose.models;   
    
    const user = await User.findOne({ key: userKey, deletedAt: { $exists: false } });

    if (!user) {
        throw new HttpError(404, 'User not found');
    }

    const retailer = await Retailer.findOne({_id: user._retailer});
    
    user.state = retailer._state;
    
    ctx.state.user = user;

    await next();
}

async function getUsers(ctx) {
    const {User} = mongoose.models;
    const payload = ctx.request.query;
    const query = {};
    const sort = payload.sort || '-createdAt';

    if (payload.status && constants.USER) {
        query.status = payload.status;
    }

    if (payload.q) {
        const regex = new RegExp(regexEscape(payload.q), 'i');
        query.$or = [
            {key: {$regex: regex}},
            {email: {$regex: regex}},
            {name: {$regex: regex}}
        ];
    }
    if (ctx.state.currentState) {
        query._state = ctx.state.currentState._id;
    }

    query.deletedAt = { $exists: false };

    const {list, count, total} = await Bluebird.props({
        list: adminUserList(query, sort)
            .skip((payload.page -1) * payload.perPage)
            .limit(payload.perPage)
            .exec(),

        count: adminUserList(query, sort).count('count'),
        total: User.count({})
    });
    ctx.body = {
        data: list,
        pagination: {
            count: count[0] && count[0].count || 0,
            total,
            page: payload.page,
            perPage: payload.perPage
        },
        filters: {...payload}
    };
}
async function getUser(ctx) {
    const user = ctx.state.user;

    ctx.body = {
        data: user.formatAdmin()
    };
}
async function deleteUser(ctx) {
    const user = ctx.state.user;
    
    await user.deleteUser();

    ctx.body = {
        data: {
            ok: true
        }
    };
}
async function patchUser(ctx) {
    const {SystemLog} = mongoose.models;
    const {status, adminNotes, _state} = ctx.request.body;
    
    const {user} = ctx.state;

    const snapshot = SystemLog.takeSnapshot(user);

    if ('adminNotes' in ctx.request.body) {
        user.set({adminNotes, processedAt: new Date()});
    }
    if ('emailVerified' in ctx.request.body) {
        user.set({emailVerified: ctx.request.body.emailVerified, processedAt: new Date()});        
    }

    if (status && status !== user.status) {
        user.set({status, processedAt: new Date()});
    }
    
    if (_state && _state !== user._state) {        
        await user.changeLocation(_state);
    }

    await user.save();
    SystemLog.adminUserUpdated(ctx, snapshot, user);
    ctx.body = {
        data: user.formatAdmin()
    };
}

function adminUserList(query, sort) {
    const {User} = mongoose.models;
    sort = {
        [sort.split('-').reverse()[0]]: sort.startsWith('-') ? -1 : 1
    };

    return User.aggregate([
        {
            $match: query
        },
        {
            $sort: sort
        },
        {
            $project: {
                _id: 0,
                id: '$_id',
                key: 1,
                name: 1,
                email: 1,
                state: 1,
                loggedAt: 1,
                status: 1,
                createdAt: 1,
                emailVerified: 1,
                lastLoginBy: 1
            }
        }
    ]);
}
