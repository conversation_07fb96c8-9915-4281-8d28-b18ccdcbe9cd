'use strict';

const config = require('../../services/config');
const mongoose = require('mongoose');
const {HttpError} = require('../../services/error');
const constants = require('../../models/constants');
const spawn = require('child_process').spawn;
const PassThrough = require('stream').PassThrough;
const url = require('url');
const Router = require('koa-router');

module.exports = Router()
    .prefix('/system')
    .get('/db', getDb);

async function getDb(ctx) {
    let dbDownloadAvailable = [constants.ENV.DEVELOPMENT, constants.ENV.STAGING].includes(config.app.env);

    if (!dbDownloadAvailable) {
        throw new HttpError(403);
    }

    let parsedUrl = url.parse(config.database);
    let dbHost = parsedUrl.host;
    let dbName = parsedUrl.pathname.slice(1);
    let archiveName = ctx.request.query.name || 'dump';

    const args = ['--db', dbName, '--host', dbHost, '--gzip', '--archive'];

    ctx.attachment(archiveName + '.gz');

    ctx.body = spawn('mongodump', args)
        .stdout
        .pipe(PassThrough());
}
