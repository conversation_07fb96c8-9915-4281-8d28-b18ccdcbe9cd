'use strict';

const hubspotSync = require('./hubspot-sync');
const { AppError } = require('./error');
const { personaClassifier } = require('./persona-classifier');

// Campaign trigger events
const TRIGGER_EVENTS = {
    USER_SIGNUP: 'user_signup',
    VENDOR_SIGNUP: 'vendor_signup',
    RETAILER_SIGNUP: 'retailer_signup',
    APPLICATION_SUBMITTED: 'application_submitted',
    APPLICATION_APPROVED: 'application_approved',
    FIRST_PRODUCT_LISTED: 'first_product_listed',
    PRODUCT_BATCH_IMPORTED: 'product_batch_imported',
    FIRST_ORDER_PLACED: 'first_order_placed',
    ORDER_COMPLETED: 'order_completed',
    PROFILE_UPDATED: 'profile_updated',
    LOGIN_AFTER_INACTIVITY: 'login_after_inactivity',
    METRC_INTEGRATION_SETUP: 'metrc_integration_setup'
};

// Event handlers registry
const eventHandlers = new Map();

// Initialize campaign trigger system
function initCampaignTriggers() {
    // Register event handlers
    registerEventHandler(TRIGGER_EVENTS.USER_SIGNUP, handleUserSignup);
    registerEventHandler(TRIGGER_EVENTS.VENDOR_SIGNUP, handleVendorSignup);
    registerEventHandler(TRIGGER_EVENTS.RETAILER_SIGNUP, handleRetailerSignup);
    registerEventHandler(TRIGGER_EVENTS.APPLICATION_SUBMITTED, handleApplicationSubmitted);
    registerEventHandler(TRIGGER_EVENTS.APPLICATION_APPROVED, handleApplicationApproved);
    registerEventHandler(TRIGGER_EVENTS.FIRST_PRODUCT_LISTED, handleFirstProductListed);
    registerEventHandler(TRIGGER_EVENTS.PRODUCT_BATCH_IMPORTED, handleProductBatchImported);
    registerEventHandler(TRIGGER_EVENTS.FIRST_ORDER_PLACED, handleFirstOrderPlaced);
    registerEventHandler(TRIGGER_EVENTS.ORDER_COMPLETED, handleOrderCompleted);
    registerEventHandler(TRIGGER_EVENTS.PROFILE_UPDATED, handleProfileUpdated);
    registerEventHandler(TRIGGER_EVENTS.LOGIN_AFTER_INACTIVITY, handleLoginAfterInactivity);
    registerEventHandler(TRIGGER_EVENTS.METRC_INTEGRATION_SETUP, handleMetrcIntegrationSetup);
    
    console.log('Campaign trigger system initialized');
}

// Register an event handler
function registerEventHandler(eventType, handler) {
    eventHandlers.set(eventType, handler);
}

// Trigger campaign based on event
async function triggerCampaign(eventType, eventData) {
    // Input validation
    if (!eventType || typeof eventType !== 'string') {
        console.error('Invalid event type for campaign trigger:', eventType);
        return { success: false, error: 'Invalid event type' };
    }

    if (!eventData || typeof eventData !== 'object') {
        console.error('Invalid event data for campaign trigger:', eventData);
        return { success: false, error: 'Invalid event data' };
    }

    // Validate required fields based on event type
    if (!eventData.userId) {
        console.error('Missing userId in event data for campaign trigger');
        return { success: false, error: 'Missing userId' };
    }

    try {
        const handler = eventHandlers.get(eventType);

        if (!handler) {
            console.warn(`No handler registered for event type: ${eventType}`);
            return { success: false, error: 'No handler found' };
        }

        console.log(`Triggering campaign for event: ${eventType}`, {
            userId: eventData.userId,
            eventType: eventType
        });

        const result = await handler(eventData);

        // Validate result
        if (!result || typeof result !== 'object') {
            console.warn(`Invalid result from campaign handler for ${eventType}`);
            return { success: false, error: 'Invalid handler result' };
        }

        console.log(`Campaign trigger result for ${eventType}:`, {
            success: result.success,
            userId: eventData.userId
        });

        return result;

    } catch (error) {
        console.error(`Error triggering campaign for ${eventType}:`, error);
        return { success: false, error: error.message || 'Unknown error' };
    }
}

// Event handler functions
async function handleUserSignup(eventData) {
    // Input validation
    if (!eventData || typeof eventData !== 'object') {
        return { success: false, error: 'Invalid event data' };
    }

    const { userId, userRole, email } = eventData;

    if (!userId) {
        return { success: false, error: 'Missing userId' };
    }

    if (!email || typeof email !== 'string') {
        return { success: false, error: 'Missing or invalid email' };
    }

    try {
        // Sync user to HubSpot with signup trigger
        const syncResult = await hubspotSync.syncUserToHubSpot(userId, 'signup');

        return {
            success: true,
            event: TRIGGER_EVENTS.USER_SIGNUP,
            userId,
            userRole: userRole || 'unknown',
            actions: ['hubspot_sync'],
            syncResult: syncResult || { success: false, error: 'No sync result' }
        };
    } catch (error) {
        console.error('Error in handleUserSignup:', error);
        return { success: false, error: error.message || 'Unknown error' };
    }
}

async function handleVendorSignup(eventData) {
    const { userId, vendorId, storeName, isDomestic } = eventData;

    try {
        // Classify user persona for targeted campaigns
        const personaClassification = await personaClassifier.classifyUserPersona(userId);

        // Sync vendor to HubSpot with vendor-specific trigger and persona data
        const syncResult = await hubspotSync.syncUserToHubSpot(userId, 'vendor_signup');

        const actions = ['hubspot_sync', 'persona_classification'];

        // Persona-based campaign selection
        const primaryPersona = personaClassification.primaryPersona.key;

        if (primaryPersona === 'STARTUP_VENDOR') {
            await hubspotSync.triggerHubSpotWorkflow(userId, 'startup_vendor_intensive_onboarding');
            actions.push('startup_intensive_onboarding');
        } else if (primaryPersona === 'ENTERPRISE_VENDOR') {
            await hubspotSync.triggerHubSpotWorkflow(userId, 'enterprise_vendor_white_glove');
            actions.push('enterprise_white_glove');
        } else if (primaryPersona === 'BOUTIQUE_VENDOR') {
            await hubspotSync.triggerHubSpotWorkflow(userId, 'boutique_vendor_premium_support');
            actions.push('boutique_premium_support');
        }

        // Geographic-based workflows
        if (isDomestic) {
            await hubspotSync.triggerHubSpotWorkflow(userId, 'domestic_vendor_onboarding');
            actions.push('domestic_vendor_workflow');
        } else {
            await hubspotSync.triggerHubSpotWorkflow(userId, 'international_vendor_onboarding');
            actions.push('international_vendor_workflow');
        }

        return {
            success: true,
            event: TRIGGER_EVENTS.VENDOR_SIGNUP,
            userId,
            vendorId,
            persona: personaClassification.primaryPersona,
            actions,
            syncResult
        };
    } catch (error) {
        return { success: false, error: error.message };
    }
}

async function handleRetailerSignup(eventData) {
    const { userId, retailerId, businessName, isDomestic } = eventData;
    
    try {
        // Sync retailer to HubSpot with retailer-specific trigger
        const syncResult = await hubspotSync.syncUserToHubSpot(userId, 'retailer_signup');
        
        const actions = ['hubspot_sync'];
        
        if (isDomestic) {
            await hubspotSync.triggerHubSpotWorkflow(userId, 'domestic_retailer_onboarding');
            actions.push('domestic_retailer_workflow');
        } else {
            await hubspotSync.triggerHubSpotWorkflow(userId, 'international_retailer_onboarding');
            actions.push('international_retailer_workflow');
        }
        
        return {
            success: true,
            event: TRIGGER_EVENTS.RETAILER_SIGNUP,
            userId,
            retailerId,
            actions,
            syncResult
        };
    } catch (error) {
        return { success: false, error: error.message };
    }
}

async function handleApplicationSubmitted(eventData) {
    const { userId, applicationType, applicationId } = eventData;
    
    try {
        // Update HubSpot with application status
        const syncResult = await hubspotSync.syncUserToHubSpot(userId, 'application_submitted');
        
        // Trigger application review workflow
        await hubspotSync.triggerHubSpotWorkflow(userId, 'application_review_process');
        
        return {
            success: true,
            event: TRIGGER_EVENTS.APPLICATION_SUBMITTED,
            userId,
            applicationType,
            actions: ['hubspot_sync', 'application_review_workflow'],
            syncResult
        };
    } catch (error) {
        return { success: false, error: error.message };
    }
}

async function handleApplicationApproved(eventData) {
    const { userId, applicationType, approvedAt } = eventData;
    
    try {
        // Update HubSpot with approval status
        const syncResult = await hubspotSync.syncUserToHubSpot(userId, 'application_approved');
        
        // Trigger post-approval onboarding workflow
        await hubspotSync.triggerHubSpotWorkflow(userId, 'post_approval_onboarding');
        
        return {
            success: true,
            event: TRIGGER_EVENTS.APPLICATION_APPROVED,
            userId,
            applicationType,
            actions: ['hubspot_sync', 'post_approval_workflow'],
            syncResult
        };
    } catch (error) {
        return { success: false, error: error.message };
    }
}

async function handleFirstProductListed(eventData) {
    const { userId, vendorId, productId, productName } = eventData;
    
    try {
        // Update HubSpot with product listing milestone
        const syncResult = await hubspotSync.syncUserToHubSpot(userId, 'first_product_listed');
        
        // Trigger product listing success workflow
        await hubspotSync.triggerHubSpotWorkflow(userId, 'first_product_success');
        
        return {
            success: true,
            event: TRIGGER_EVENTS.FIRST_PRODUCT_LISTED,
            userId,
            vendorId,
            productId,
            actions: ['hubspot_sync', 'product_success_workflow'],
            syncResult
        };
    } catch (error) {
        return { success: false, error: error.message };
    }
}

async function handleProductBatchImported(eventData) {
    const { userId, vendorId, importedCount, totalProducts } = eventData;
    
    try {
        // Update HubSpot with batch import milestone
        const syncResult = await hubspotSync.syncUserToHubSpot(userId, 'product_batch_imported');
        
        // Trigger batch import success workflow
        await hubspotSync.triggerHubSpotWorkflow(userId, 'batch_import_success');
        
        return {
            success: true,
            event: TRIGGER_EVENTS.PRODUCT_BATCH_IMPORTED,
            userId,
            vendorId,
            importedCount,
            actions: ['hubspot_sync', 'batch_import_workflow'],
            syncResult
        };
    } catch (error) {
        return { success: false, error: error.message };
    }
}

async function handleFirstOrderPlaced(eventData) {
    const { userId, retailerId, orderId, orderTotal } = eventData;
    
    try {
        // Update HubSpot with first order milestone
        const syncResult = await hubspotSync.syncUserToHubSpot(userId, 'first_order_placed');
        
        // Trigger first order success workflow
        await hubspotSync.triggerHubSpotWorkflow(userId, 'first_order_success');
        
        return {
            success: true,
            event: TRIGGER_EVENTS.FIRST_ORDER_PLACED,
            userId,
            retailerId,
            orderId,
            actions: ['hubspot_sync', 'first_order_workflow'],
            syncResult
        };
    } catch (error) {
        return { success: false, error: error.message };
    }
}

async function handleOrderCompleted(eventData) {
    const { userId, orderId, orderTotal, isRepeatCustomer } = eventData;
    
    try {
        // Update HubSpot with order completion
        const syncResult = await hubspotSync.syncUserToHubSpot(userId, 'order_completed');
        
        const actions = ['hubspot_sync'];
        
        if (isRepeatCustomer) {
            // Trigger repeat customer workflow
            await hubspotSync.triggerHubSpotWorkflow(userId, 'repeat_customer_engagement');
            actions.push('repeat_customer_workflow');
        }
        
        return {
            success: true,
            event: TRIGGER_EVENTS.ORDER_COMPLETED,
            userId,
            orderId,
            actions,
            syncResult
        };
    } catch (error) {
        return { success: false, error: error.message };
    }
}

async function handleProfileUpdated(eventData) {
    const { userId, updatedFields } = eventData;
    
    try {
        // Sync updated profile to HubSpot
        const syncResult = await hubspotSync.syncUserToHubSpot(userId, 'profile_updated');
        
        return {
            success: true,
            event: TRIGGER_EVENTS.PROFILE_UPDATED,
            userId,
            updatedFields,
            actions: ['hubspot_sync'],
            syncResult
        };
    } catch (error) {
        return { success: false, error: error.message };
    }
}

async function handleLoginAfterInactivity(eventData) {
    const { userId, daysSinceLastLogin } = eventData;
    
    try {
        // Update HubSpot with re-engagement
        const syncResult = await hubspotSync.syncUserToHubSpot(userId, 'login_after_inactivity');
        
        // Trigger re-engagement workflow
        await hubspotSync.triggerHubSpotWorkflow(userId, 'user_re_engagement');
        
        return {
            success: true,
            event: TRIGGER_EVENTS.LOGIN_AFTER_INACTIVITY,
            userId,
            daysSinceLastLogin,
            actions: ['hubspot_sync', 're_engagement_workflow'],
            syncResult
        };
    } catch (error) {
        return { success: false, error: error.message };
    }
}

async function handleMetrcIntegrationSetup(eventData) {
    const { userId, vendorId, licenseNumber } = eventData;
    
    try {
        // Update HubSpot with Metrc integration milestone
        const syncResult = await hubspotSync.syncUserToHubSpot(userId, 'metrc_integration_setup');
        
        // Trigger Metrc integration success workflow
        await hubspotSync.triggerHubSpotWorkflow(userId, 'metrc_integration_success');
        
        return {
            success: true,
            event: TRIGGER_EVENTS.METRC_INTEGRATION_SETUP,
            userId,
            vendorId,
            actions: ['hubspot_sync', 'metrc_integration_workflow'],
            syncResult
        };
    } catch (error) {
        return { success: false, error: error.message };
    }
}

module.exports = {
    initCampaignTriggers,
    triggerCampaign,
    registerEventHandler,
    TRIGGER_EVENTS
};
