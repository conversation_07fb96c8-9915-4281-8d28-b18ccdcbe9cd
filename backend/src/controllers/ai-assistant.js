'use strict';

const Router = require('koa-router');
const mongoose = require('mongoose');
const { HttpError } = require('../services/error');
const openaiService = require('../services/openai');
const constants = require('../models/constants');
const { executeAction, getAvailableActions } = require('../services/ai-actions/action-executor');
const { initUserActions } = require('../services/ai-actions/user-actions');
const { updateConversationContext, getConversationStats } = require('../services/ai-conversation/conversation-manager');

// Initialize action handlers
initUserActions();

const aiAssistantRouter = Router()
    .prefix('/ai-assistant')
    .post('/chat', postChat)
    .get('/suggestions', getSuggestions)
    .post('/analyze-intent', postAnalyzeIntent)
    .post('/execute-action', postExecuteAction)
    .get('/available-actions', getAvailableActionsEndpoint)
    .post('/update-context', postUpdateContext)
    .get('/conversation-stats', getConversationStatsEndpoint);

module.exports = aiAssistantRouter;

// Handle chat messages with AI assistant
async function postChat(ctx) {
    const { message, context = {} } = ctx.request.body;
    
    if (!message || typeof message !== 'string' || message.trim().length === 0) {
        throw new HttpError(422, 'Message is required');
    }
    
    if (message.length > 1000) {
        throw new HttpError(422, 'Message too long');
    }
    
    try {
        // Build context from current user and session
        const userContext = buildUserContext(ctx, context);
        
        // Generate AI response
        const response = await openaiService.generateAssistantResponse(message, userContext);
        
        // Log the interaction for analytics
        await logAssistantInteraction(ctx, message, response.message, userContext);
        
        ctx.body = {
            data: {
                message: response.message,
                context: userContext,
                timestamp: new Date().toISOString()
            }
        };
    } catch (error) {
        if (error.status) {
            throw error;
        }
        console.error('AI Assistant error:', error);
        throw new HttpError(500, 'Assistant temporarily unavailable');
    }
}

// Get contextual suggestions for the user
async function getSuggestions(ctx) {
    try {
        const userContext = buildUserContext(ctx);
        const suggestions = await openaiService.generateSuggestions(userContext);
        
        ctx.body = {
            data: {
                suggestions,
                context: userContext
            }
        };
    } catch (error) {
        console.error('Error getting suggestions:', error);
        ctx.body = {
            data: {
                suggestions: getDefaultSuggestions(ctx.state.user),
                context: buildUserContext(ctx)
            }
        };
    }
}

// Analyze user intent for better assistance
async function postAnalyzeIntent(ctx) {
    const { message, context = {} } = ctx.request.body;
    
    if (!message) {
        throw new HttpError(422, 'Message is required');
    }
    
    try {
        const userContext = buildUserContext(ctx, context);
        const analysis = await openaiService.analyzeUserIntent(message, userContext);
        
        ctx.body = {
            data: analysis
        };
    } catch (error) {
        console.error('Error analyzing intent:', error);
        ctx.body = {
            data: {
                intent: 'unknown',
                confidence: 0,
                actions: [],
                category: 'general'
            }
        };
    }
}

// Build comprehensive user context for AI
function buildUserContext(ctx, additionalContext = {}) {
    const { user, vendor, retailer } = ctx.state;
    const userRole = getUserRole(ctx);
    
    const context = {
        userRole,
        userId: user?.key,
        currentPage: additionalContext.currentPage || ctx.path,
        userPermissions: getUserPermissions(ctx),
        availableActions: getAvailableActions(ctx),
        ...additionalContext
    };
    
    // Add role-specific context
    if (vendor && userRole === 'vendor') {
        context.vendorInfo = {
            storeName: vendor.storeName,
            enabled: vendor.enabled,
            hasApplication: !!vendor._application,
            productCount: additionalContext.productCount || 0
        };
    }
    
    if (retailer && userRole === 'retailer') {
        context.retailerInfo = {
            businessName: retailer.businessName,
            enabled: retailer.enabled,
            hasApplication: !!retailer._application,
            budget: retailer.retailerBudget
        };
    }
    
    return context;
}

// Determine user role from context
function getUserRole(ctx) {
    const path = ctx.path;
    
    if (path.startsWith('/admin')) return 'admin';
    if (path.startsWith('/vendor')) return 'vendor';
    if (path.startsWith('/retailer')) return 'retailer';
    
    // Fallback to checking user's associated entities
    if (ctx.state.vendor) return 'vendor';
    if (ctx.state.retailer) return 'retailer';
    
    return 'user';
}

// Get user permissions based on role and context
function getUserPermissions(ctx) {
    const role = getUserRole(ctx);
    const permissions = [];
    
    switch (role) {
        case 'admin':
            permissions.push('manage_users', 'manage_applications', 'view_analytics', 'manage_platform');
            break;
        case 'vendor':
            if (ctx.state.vendor?.enabled) {
                permissions.push('manage_products', 'view_orders', 'manage_profile', 'create_tickets');
            }
            permissions.push('view_application', 'update_profile');
            break;
        case 'retailer':
            if (ctx.state.retailer?.enabled) {
                permissions.push('browse_products', 'place_orders', 'manage_profile', 'create_tickets');
            }
            permissions.push('view_application', 'update_profile');
            break;
    }
    
    return permissions;
}

// Get available actions based on current context
function getAvailableActions(ctx) {
    const role = getUserRole(ctx);
    const path = ctx.path;
    const actions = [];
    
    // Common actions
    actions.push('change_password', 'update_profile', 'contact_support');
    
    // Role-specific actions
    if (role === 'vendor') {
        actions.push('add_product', 'view_orders', 'manage_inventory', 'update_application');
        if (path.includes('/products')) {
            actions.push('create_product', 'import_products', 'manage_categories');
        }
    } else if (role === 'retailer') {
        actions.push('browse_products', 'place_order', 'view_order_history', 'update_application');
        if (path.includes('/products')) {
            actions.push('search_products', 'filter_products', 'add_to_cart');
        }
    } else if (role === 'admin') {
        actions.push('manage_users', 'review_applications', 'view_analytics', 'manage_platform');
    }
    
    return actions;
}

// Log assistant interactions for analytics and improvement
async function logAssistantInteraction(ctx, userMessage, aiResponse, context) {
    try {
        const { SystemLog } = mongoose.models;
        
        // Create a system log entry for the AI interaction
        const logData = {
            type: 'ai_assistant_interaction',
            userMessage: userMessage.substring(0, 500), // Limit length
            aiResponse: aiResponse.substring(0, 500),
            context: {
                userRole: context.userRole,
                currentPage: context.currentPage,
                userId: context.userId
            },
            timestamp: new Date()
        };
        
        // This would need to be implemented in the SystemLog model
        // For now, just console.log for debugging
        console.log('AI Assistant Interaction:', logData);
        
    } catch (error) {
        console.error('Error logging AI interaction:', error);
        // Don't throw - logging failures shouldn't break the main functionality
    }
}

// Fallback suggestions when AI is unavailable
function getDefaultSuggestions(user) {
    const suggestions = [
        "How do I change my password?",
        "How do I update my profile?",
        "How do I contact support?",
        "What are the platform features?"
    ];
    
    // Add role-specific suggestions
    if (user?._vendor) {
        suggestions.unshift(
            "How do I add a new product?",
            "How do I manage my inventory?",
            "How do I view my orders?"
        );
    } else if (user?._retailer) {
        suggestions.unshift(
            "How do I search for products?",
            "How do I place an order?",
            "How do I view my order history?"
        );
    }
    
    return suggestions.slice(0, 5); // Limit to 5 suggestions
}

// Execute an AI-suggested action
async function postExecuteAction(ctx) {
    const { action, params = {} } = ctx.request.body;

    if (!action || typeof action !== 'string') {
        throw new HttpError(422, 'Action name is required');
    }

    try {
        const userContext = buildUserContext(ctx);
        const result = await executeAction(action, params, userContext);

        ctx.body = {
            data: result
        };
    } catch (error) {
        if (error.status) {
            throw error;
        }
        console.error('Action execution error:', error);
        throw new HttpError(500, 'Failed to execute action');
    }
}

// Get available actions for the current user
async function getAvailableActionsEndpoint(ctx) {
    try {
        const userContext = buildUserContext(ctx);
        const actions = getAvailableActions(userContext);

        ctx.body = {
            data: {
                actions,
                context: userContext
            }
        };
    } catch (error) {
        console.error('Error getting available actions:', error);
        ctx.body = {
            data: {
                actions: [],
                context: buildUserContext(ctx)
            }
        };
    }
}

// Update conversation context
async function postUpdateContext(ctx) {
    const { contextUpdates = {} } = ctx.request.body;

    if (typeof contextUpdates !== 'object') {
        throw new HttpError(422, 'Context updates must be an object');
    }

    try {
        const userContext = buildUserContext(ctx);

        if (userContext.userId && userContext.userRole) {
            updateConversationContext(userContext.userId, userContext.userRole, contextUpdates);
        }

        ctx.body = {
            data: {
                message: 'Context updated successfully',
                updates: contextUpdates
            }
        };
    } catch (error) {
        console.error('Error updating context:', error);
        throw new HttpError(500, 'Failed to update context');
    }
}

// Get conversation statistics (admin only)
async function getConversationStatsEndpoint(ctx) {
    const userRole = getUserRole(ctx);

    if (userRole !== 'admin') {
        throw new HttpError(403, 'Admin access required');
    }

    try {
        const stats = getConversationStats();

        ctx.body = {
            data: stats
        };
    } catch (error) {
        console.error('Error getting conversation stats:', error);
        ctx.body = {
            data: {
                error: 'Failed to retrieve statistics'
            }
        };
    }
}
