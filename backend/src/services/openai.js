'use strict';

const OpenAI = require('openai');
const config = require('./config');
const constants = require('../models/constants');
const { AppError } = require('./error');
const { getKnowledgeForRole, getCommonTask, searchKnowledge } = require('./ai-knowledge/platform-knowledge');
const { getAvailableActions } = require('./ai-actions/action-executor');
const { buildEnhancedContext, addMessage } = require('./ai-conversation/conversation-manager');

let openai;

// Initialize OpenAI client
function init() {
    if (!config.openai || !config.openai.apiKey) {
        console.warn('OpenAI API key not configured');
        return;
    }
    
    openai = new OpenAI({
        apiKey: config.openai.apiKey,
    });
}

// Rate limiting and error handling
const rateLimiter = {
    requests: [],
    maxRequests: 60, // per minute
    windowMs: 60 * 1000,
    
    canMakeRequest() {
        const now = Date.now();
        this.requests = this.requests.filter(time => now - time < this.windowMs);
        return this.requests.length < this.maxRequests;
    },
    
    recordRequest() {
        this.requests.push(Date.now());
    }
};

// Generate AI response for user assistance
async function generateAssistantResponse(userMessage, context = {}) {
    // Input validation
    if (!userMessage || typeof userMessage !== 'string') {
        throw new AppError(422, 'User message is required and must be a string');
    }

    if (userMessage.trim().length === 0) {
        throw new AppError(422, 'User message cannot be empty');
    }

    if (userMessage.length > 4000) {
        throw new AppError(422, 'User message is too long (max 4000 characters)');
    }

    if (!openai) {
        throw new AppError(503, 'AI service not available');
    }

    if (!rateLimiter.canMakeRequest()) {
        throw new AppError(429, 'Rate limit exceeded. Please try again later.');
    }

    try {
        rateLimiter.recordRequest();

        // Sanitize context to prevent injection
        const sanitizedContext = sanitizeContext(context);

        // Build enhanced context with conversation history
        const enhancedContext = buildEnhancedContext(sanitizedContext, sanitizedContext.userId, sanitizedContext.userRole);

        // Add user message to conversation history (with error handling)
        if (sanitizedContext.userId && sanitizedContext.userRole) {
            try {
                addMessage(sanitizedContext.userId, sanitizedContext.userRole, 'user', userMessage);
            } catch (conversationError) {
                console.warn('Failed to add message to conversation history:', conversationError);
                // Continue without conversation history rather than failing
            }
        }

        const systemPrompt = buildSystemPrompt(enhancedContext);
        const messages = buildConversationMessages(enhancedContext, userMessage);

        // Validate messages array
        if (!Array.isArray(messages) || messages.length === 0) {
            throw new Error('Invalid messages array for OpenAI API');
        }

        const response = await openai.chat.completions.create({
            model: 'gpt-4',
            messages,
            max_tokens: 500,
            temperature: 0.7,
        });

        // Validate OpenAI response
        if (!response || !response.choices || response.choices.length === 0) {
            throw new Error('Invalid response from OpenAI API');
        }

        const aiResponse = response.choices[0]?.message?.content;
        if (!aiResponse) {
            throw new Error('Empty response from OpenAI API');
        }

        // Add AI response to conversation history (with error handling)
        if (sanitizedContext.userId && sanitizedContext.userRole) {
            try {
                addMessage(sanitizedContext.userId, sanitizedContext.userRole, 'ai', aiResponse, {
                    usage: response.usage
                });
            } catch (conversationError) {
                console.warn('Failed to add AI response to conversation history:', conversationError);
                // Continue without saving to history rather than failing
            }
        }

        return {
            message: aiResponse,
            usage: response.usage || {}
        };
    } catch (error) {
        console.error('OpenAI API error:', error);

        // Handle specific OpenAI API errors
        if (error.status === 429) {
            throw new AppError(429, 'AI service is busy. Please try again in a moment.');
        } else if (error.status === 401 || error.status === 403) {
            throw new AppError(503, 'AI service configuration error');
        } else if (error.status === 400) {
            throw new AppError(422, 'Invalid request to AI service');
        } else if (error.code === 'ENOTFOUND' || error.code === 'ECONNREFUSED') {
            throw new AppError(503, 'AI service is unreachable');
        } else if (error instanceof AppError) {
            throw error; // Re-throw our own errors
        } else {
            throw new AppError(500, 'AI service temporarily unavailable');
        }
    }
}

// Sanitize context to prevent injection and ensure data integrity
function sanitizeContext(context) {
    if (!context || typeof context !== 'object') {
        return {};
    }

    const sanitized = {};

    // Only allow specific context fields and sanitize them
    const allowedFields = ['userId', 'userRole', 'currentPage', 'userAgent', 'sessionId'];

    for (const field of allowedFields) {
        if (context[field] !== undefined && context[field] !== null) {
            if (typeof context[field] === 'string') {
                // Sanitize strings - remove potential injection attempts
                sanitized[field] = context[field]
                    .replace(/[<>]/g, '') // Remove HTML tags
                    .replace(/javascript:/gi, '') // Remove javascript: protocol
                    .replace(/on\w+=/gi, '') // Remove event handlers
                    .trim()
                    .substring(0, 500); // Limit length
            } else if (typeof context[field] === 'number' || typeof context[field] === 'boolean') {
                sanitized[field] = context[field];
            }
        }
    }

    return sanitized;
}

// Build context-aware system prompt
function buildSystemPrompt(context) {
    const { userRole, currentPage, userPermissions, availableActions } = context;

    // Get role-specific knowledge
    const roleKnowledge = getKnowledgeForRole(userRole);
    const availableActionsInfo = getAvailableActions(context);

    let prompt = `You are a helpful AI assistant for the CanIDeal cannabis B2B marketplace platform.
Your role is to help users navigate and use the platform effectively.

Current context:
- User role: ${userRole || 'unknown'}
- Current page: ${currentPage || 'unknown'}
- User permissions: ${userPermissions ? userPermissions.join(', ') : 'none'}
- Available actions: ${availableActions ? availableActions.join(', ') : 'none specified'}

${roleKnowledge ? `
Role-specific capabilities:
${roleKnowledge.capabilities.map(cap => `- ${cap}`).join('\n')}

Common tasks for ${userRole}s:
${roleKnowledge.commonTasks.map(task => `- ${task}`).join('\n')}

Dashboard sections:
${roleKnowledge.dashboardSections.map(section => `- ${section}`).join('\n')}
` : ''}

Guidelines:
1. Be helpful, concise, and professional
2. Focus on platform-specific assistance using the knowledge above
3. Provide step-by-step instructions for common tasks
4. For account settings, guide users to the appropriate sections
5. For technical issues, provide clear troubleshooting steps
6. Always respect user permissions and roles
7. If you cannot help with something, suggest contacting support
8. Use the available actions when appropriate to help users
9. Be specific about where to find features (e.g., "Go to Settings > Profile")

Platform-specific knowledge:
- CanIDeal connects cannabis vendors with retailers in a B2B marketplace
- Vendors can list products, manage inventory, and fulfill orders
- Retailers can browse products, place orders, and manage their business
- Admins can manage users, applications, and platform operations
- The platform includes features for compliance, payments, and communication
- Metrc integration is available for domestic vendors for compliance tracking
- Ticket system enables communication between vendors and retailers
- Spreadsheet import allows vendors to bulk upload products

Respond in a helpful, friendly tone and provide actionable guidance with specific locations and steps.`;

    return prompt;
}

// Build conversation messages including history
function buildConversationMessages(context, currentMessage) {
    const messages = [
        { role: 'system', content: buildSystemPrompt(context) }
    ];

    // Add recent conversation history if available
    if (context.conversation && context.conversation.recentMessages) {
        const recentMessages = context.conversation.recentMessages.slice(-6); // Last 6 messages

        recentMessages.forEach(msg => {
            if (msg.type === 'user') {
                messages.push({ role: 'user', content: msg.content });
            } else if (msg.type === 'ai') {
                messages.push({ role: 'assistant', content: msg.content });
            }
        });
    }

    // Add current message
    messages.push({ role: 'user', content: currentMessage });

    return messages;
}

// Generate suggestions based on user context
async function generateSuggestions(context) {
    if (!openai) {
        return getDefaultSuggestions(context);
    }

    try {
        // Sanitize context for suggestions
        const sanitizedContext = sanitizeContext(context);

        // Limit context size to prevent token overflow
        const contextString = JSON.stringify(sanitizedContext, null, 2);
        if (contextString.length > 1000) {
            console.warn('Context too large for suggestions, using default suggestions');
            return getDefaultSuggestions(sanitizedContext);
        }

        const prompt = `Based on the user context, suggest 3-5 helpful actions or questions they might want to ask:

Context: ${contextString}

Return only a JSON array of suggestion strings, no other text.`;

        const response = await openai.chat.completions.create({
            model: 'gpt-3.5-turbo',
            messages: [{ role: 'user', content: prompt }],
            max_tokens: 200,
            temperature: 0.5,
        });

        if (!response?.choices?.[0]?.message?.content) {
            return getDefaultSuggestions(sanitizedContext);
        }

        const content = response.choices[0].message.content.trim();

        // Try to parse JSON response
        let suggestions;
        try {
            suggestions = JSON.parse(content);
        } catch (parseError) {
            console.warn('Failed to parse suggestions JSON:', parseError);
            return getDefaultSuggestions(sanitizedContext);
        }

        // Validate suggestions array
        if (!Array.isArray(suggestions)) {
            return getDefaultSuggestions(sanitizedContext);
        }

        // Filter and validate individual suggestions
        const validSuggestions = suggestions
            .filter(s => typeof s === 'string' && s.trim().length > 0)
            .map(s => s.trim().substring(0, 200)) // Limit suggestion length
            .slice(0, 5); // Limit to 5 suggestions

        return validSuggestions.length > 0 ? validSuggestions : getDefaultSuggestions(sanitizedContext);

    } catch (error) {
        console.error('Error generating suggestions:', error);
        return getDefaultSuggestions(context);
    }
}

// Get default suggestions based on user role
function getDefaultSuggestions(context) {
    const userRole = context?.userRole;

    const defaultSuggestions = {
        vendor: [
            "How do I add a new product?",
            "How do I manage my inventory?",
            "How do I view my orders?",
            "How do I update my profile?",
            "How do I contact support?"
        ],
        retailer: [
            "How do I search for products?",
            "How do I place an order?",
            "How do I track my orders?",
            "How do I contact a vendor?",
            "How do I update my business information?"
        ],
        admin: [
            "How do I review applications?",
            "How do I manage users?",
            "How do I view platform analytics?",
            "How do I send email campaigns?",
            "How do I handle support tickets?"
        ]
    };

    return defaultSuggestions[userRole] || [
        "How do I change my password?",
        "How do I update my profile?",
        "How do I contact support?",
        "What features are available to me?",
        "How do I navigate the platform?"
    ];
}

// Analyze user intent and extract actionable items
async function analyzeUserIntent(message, context) {
    // Input validation
    if (!message || typeof message !== 'string' || message.trim().length === 0) {
        return { intent: 'unknown', confidence: 0, actions: [], category: 'general' };
    }

    if (!openai) {
        return analyzeIntentFallback(message, context);
    }

    try {
        // Sanitize inputs
        const sanitizedMessage = message.trim().substring(0, 1000); // Limit message length
        const sanitizedContext = sanitizeContext(context);

        const contextString = JSON.stringify(sanitizedContext, null, 2);
        if (contextString.length > 500) {
            // Use fallback for large contexts
            return analyzeIntentFallback(sanitizedMessage, sanitizedContext);
        }

        const prompt = `Analyze this user message and determine their intent and any actions they want to perform:

Message: "${sanitizedMessage}"
Context: ${contextString}

Return a JSON object with:
{
  "intent": "category of request (help, action, question, etc.)",
  "confidence": 0.0-1.0,
  "actions": ["list of specific actions they want to perform"],
  "category": "settings|products|orders|account|general"
}`;

        const response = await openai.chat.completions.create({
            model: 'gpt-3.5-turbo',
            messages: [{ role: 'user', content: prompt }],
            max_tokens: 150,
            temperature: 0.3,
        });

        if (!response?.choices?.[0]?.message?.content) {
            return analyzeIntentFallback(sanitizedMessage, sanitizedContext);
        }

        const content = response.choices[0].message.content.trim();

        let intentAnalysis;
        try {
            intentAnalysis = JSON.parse(content);
        } catch (parseError) {
            console.warn('Failed to parse intent analysis JSON:', parseError);
            return analyzeIntentFallback(sanitizedMessage, sanitizedContext);
        }

        // Validate and sanitize the response
        const validatedAnalysis = {
            intent: typeof intentAnalysis.intent === 'string' ? intentAnalysis.intent : 'unknown',
            confidence: typeof intentAnalysis.confidence === 'number' ?
                Math.max(0, Math.min(1, intentAnalysis.confidence)) : 0,
            actions: Array.isArray(intentAnalysis.actions) ?
                intentAnalysis.actions.filter(a => typeof a === 'string').slice(0, 5) : [],
            category: ['settings', 'products', 'orders', 'account', 'general'].includes(intentAnalysis.category) ?
                intentAnalysis.category : 'general'
        };

        return validatedAnalysis;

    } catch (error) {
        console.error('Error analyzing intent:', error);
        return analyzeIntentFallback(message, context);
    }
}

// Fallback intent analysis using simple keyword matching
function analyzeIntentFallback(message, context) {
    const lowerMessage = message.toLowerCase();

    // Simple keyword-based intent detection
    const intentKeywords = {
        help: ['help', 'how', 'what', 'guide', 'tutorial', 'explain'],
        action: ['change', 'update', 'create', 'add', 'delete', 'remove', 'set'],
        question: ['?', 'why', 'when', 'where', 'who', 'which'],
        navigation: ['go', 'navigate', 'find', 'locate', 'show', 'display']
    };

    const categoryKeywords = {
        settings: ['password', 'profile', 'settings', 'account', 'preferences'],
        products: ['product', 'inventory', 'item', 'catalog', 'listing'],
        orders: ['order', 'purchase', 'buy', 'cart', 'checkout'],
        account: ['account', 'profile', 'information', 'details']
    };

    let intent = 'unknown';
    let category = 'general';
    let confidence = 0.3; // Low confidence for fallback

    // Detect intent
    for (const [intentType, keywords] of Object.entries(intentKeywords)) {
        if (keywords.some(keyword => lowerMessage.includes(keyword))) {
            intent = intentType;
            confidence = 0.6;
            break;
        }
    }

    // Detect category
    for (const [categoryType, keywords] of Object.entries(categoryKeywords)) {
        if (keywords.some(keyword => lowerMessage.includes(keyword))) {
            category = categoryType;
            break;
        }
    }

    return {
        intent,
        confidence,
        actions: [],
        category
    };
}

module.exports = {
    init,
    generateAssistantResponse,
    generateSuggestions,
    analyzeUserIntent
};
