'use strict';

const userAnalytics = require('./user-analytics');
const { AppError } = require('./error');

// Enhanced tracking events
const TRACKING_EVENTS = {
    // Page views
    PAGE_VIEW: 'page_view',
    DASHBOARD_VIEW: 'dashboard_view',
    PRODUCT_VIEW: 'product_view',
    PROFILE_VIEW: 'profile_view',
    
    // User actions
    SEARCH_PERFORMED: 'search_performed',
    FILTER_APPLIED: 'filter_applied',
    PRODUCT_CLICKED: 'product_clicked',
    VENDOR_CONTACTED: 'vendor_contacted',
    
    // Business actions
    PRODUCT_CREATED: 'product_created',
    PRODUCT_UPDATED: 'product_updated',
    ORDER_PLACED: 'order_placed',
    ORDER_COMPLETED: 'order_completed',
    
    // Engagement
    HELP_ACCESSED: 'help_accessed',
    AI_ASSISTANT_USED: 'ai_assistant_used',
    SUPPORT_TICKET_CREATED: 'support_ticket_created',
    
    // Technical
    LOGIN: 'login',
    LOGOUT: 'logout',
    SESSION_TIMEOUT: 'session_timeout',
    ERROR_ENCOUNTERED: 'error_encountered',

    // Enhanced Demographics & Business Tracking
    DEMOGRAPHIC_UPDATE: 'demographic_update',
    BUSINESS_SIZE_DETECTED: 'business_size_detected',
    PURCHASE_HISTORY_ANALYZED: 'purchase_history_analyzed',
    BROWSING_PATTERN_DETECTED: 'browsing_pattern_detected',
    DEVICE_FINGERPRINT: 'device_fingerprint',
    LOCATION_DETECTED: 'location_detected',
    REFERRAL_SOURCE_TRACKED: 'referral_source_tracked',

    // Advanced Behavioral Patterns
    ENGAGEMENT_MILESTONE: 'engagement_milestone',
    CONVERSION_FUNNEL_STEP: 'conversion_funnel_step',
    ABANDONMENT_EVENT: 'abandonment_event',
    RETURN_VISIT: 'return_visit',
    FEATURE_ADOPTION: 'feature_adoption',
    TIME_SPENT_MILESTONE: 'time_spent_milestone'
};

// Track user behavior with enhanced context
async function trackUserBehavior(userId, eventType, context = {}) {
    try {
        const enhancedContext = {
            ...context,
            timestamp: new Date(),
            userAgent: context.userAgent || 'unknown',
            ipAddress: context.ipAddress || 'unknown',
            sessionId: context.sessionId || 'unknown',
            referrer: context.referrer || 'direct'
        };
        
        await userAnalytics.trackBehavior(userId, eventType, enhancedContext);
        
        // Trigger real-time analysis for important events
        if (isHighValueEvent(eventType)) {
            await analyzeUserBehaviorRealTime(userId, eventType, enhancedContext);
        }
        
        return { success: true, eventType, timestamp: enhancedContext.timestamp };
        
    } catch (error) {
        console.error('Error tracking user behavior:', error);
        return { success: false, error: error.message };
    }
}

// Check if event should trigger real-time analysis
function isHighValueEvent(eventType) {
    const highValueEvents = [
        TRACKING_EVENTS.PRODUCT_CREATED,
        TRACKING_EVENTS.ORDER_PLACED,
        TRACKING_EVENTS.ORDER_COMPLETED,
        TRACKING_EVENTS.VENDOR_CONTACTED,
        TRACKING_EVENTS.AI_ASSISTANT_USED
    ];
    
    return highValueEvents.includes(eventType);
}

// Real-time behavior analysis
async function analyzeUserBehaviorRealTime(userId, eventType, context) {
    try {
        // Update user persona based on new behavior
        const persona = await userAnalytics.analyzeUserPersona(userId);
        
        // Trigger appropriate actions based on persona and event
        await triggerPersonaBasedActions(userId, persona, eventType, context);
        
    } catch (error) {
        console.error('Error in real-time behavior analysis:', error);
    }
}

// Trigger actions based on persona analysis
async function triggerPersonaBasedActions(userId, persona, eventType, context) {
    try {
        const campaignTriggers = require('./campaign-triggers');
        
        // High conversion likelihood users get priority treatment
        if (persona.conversionLikelihood > 0.8) {
            console.log(`High-value user detected: ${userId}, likelihood: ${persona.conversionLikelihood}`);
            
            // Trigger high-value user workflow
            await campaignTriggers.triggerCampaign('high_value_user_detected', {
                userId,
                persona: persona.primaryPersona,
                conversionLikelihood: persona.conversionLikelihood,
                triggerEvent: eventType
            });
        }
        
        // New users who show engagement get onboarding boost
        if (persona.primaryPersona.includes('new_') && persona.conversionLikelihood > 0.5) {
            await campaignTriggers.triggerCampaign('engaged_new_user', {
                userId,
                persona: persona.primaryPersona,
                triggerEvent: eventType
            });
        }
        
        // Users with low engagement get re-engagement campaigns
        if (persona.conversionLikelihood < 0.3) {
            await campaignTriggers.triggerCampaign('low_engagement_user', {
                userId,
                persona: persona.primaryPersona,
                triggerEvent: eventType
            });
        }
        
    } catch (error) {
        console.error('Error triggering persona-based actions:', error);
    }
}

// Track page views with enhanced context
async function trackPageView(userId, pagePath, context = {}) {
    const pageContext = {
        ...context,
        pagePath,
        pageType: categorizePageType(pagePath),
        timeOnPage: context.timeOnPage || 0,
        scrollDepth: context.scrollDepth || 0
    };
    
    return await trackUserBehavior(userId, TRACKING_EVENTS.PAGE_VIEW, pageContext);
}

// Categorize page type for analysis
function categorizePageType(pagePath) {
    if (pagePath.includes('/dashboard')) return 'dashboard';
    if (pagePath.includes('/products')) return 'products';
    if (pagePath.includes('/orders')) return 'orders';
    if (pagePath.includes('/settings')) return 'settings';
    if (pagePath.includes('/profile')) return 'profile';
    if (pagePath.includes('/help')) return 'help';
    return 'other';
}

// Track search behavior
async function trackSearch(userId, searchQuery, results, context = {}) {
    const searchContext = {
        ...context,
        searchQuery,
        resultCount: results.length,
        searchType: context.searchType || 'general',
        filters: context.filters || {},
        sortBy: context.sortBy || 'relevance'
    };
    
    return await trackUserBehavior(userId, TRACKING_EVENTS.SEARCH_PERFORMED, searchContext);
}

// Track product interactions
async function trackProductInteraction(userId, productId, interactionType, context = {}) {
    const productContext = {
        ...context,
        productId,
        interactionType, // 'view', 'click', 'add_to_cart', 'contact_vendor'
        productCategory: context.productCategory || 'unknown',
        vendorId: context.vendorId || 'unknown',
        price: context.price || 0
    };
    
    const eventType = interactionType === 'view' ? TRACKING_EVENTS.PRODUCT_VIEW : TRACKING_EVENTS.PRODUCT_CLICKED;
    return await trackUserBehavior(userId, eventType, productContext);
}

// Track business actions
async function trackBusinessAction(userId, actionType, actionData, context = {}) {
    const businessContext = {
        ...context,
        actionType,
        actionData,
        businessImpact: calculateBusinessImpact(actionType, actionData)
    };
    
    let eventType;
    switch (actionType) {
        case 'product_created':
            eventType = TRACKING_EVENTS.PRODUCT_CREATED;
            break;
        case 'order_placed':
            eventType = TRACKING_EVENTS.ORDER_PLACED;
            break;
        case 'order_completed':
            eventType = TRACKING_EVENTS.ORDER_COMPLETED;
            break;
        default:
            eventType = 'business_action';
    }
    
    return await trackUserBehavior(userId, eventType, businessContext);
}

// Calculate business impact score
function calculateBusinessImpact(actionType, actionData) {
    switch (actionType) {
        case 'product_created':
            return 'medium'; // New product adds inventory value
        case 'order_placed':
            return actionData.orderValue > 1000 ? 'high' : 'medium';
        case 'order_completed':
            return 'high'; // Completed orders are high value
        default:
            return 'low';
    }
}

// Track AI assistant usage
async function trackAIAssistantUsage(userId, interactionType, context = {}) {
    const aiContext = {
        ...context,
        interactionType, // 'chat_opened', 'message_sent', 'action_executed'
        messageCount: context.messageCount || 1,
        actionExecuted: context.actionExecuted || null,
        satisfactionRating: context.satisfactionRating || null
    };
    
    return await trackUserBehavior(userId, TRACKING_EVENTS.AI_ASSISTANT_USED, aiContext);
}

// Get user behavior insights
async function getUserBehaviorInsights(userId) {
    try {
        const behavior = userAnalytics.getUserBehavior(userId);
        const persona = await userAnalytics.analyzeUserPersona(userId);
        
        if (!behavior) {
            return { error: 'No behavior data found' };
        }
        
        const insights = {
            persona,
            behaviorSummary: {
                totalEvents: behavior.events.length,
                lastActivity: behavior.lastActivity,
                sessionCount: getUniqueSessionCount(behavior.events),
                mostCommonActions: getMostCommonActions(behavior.events),
                engagementScore: calculateEngagementScore(behavior.events)
            },
            recommendations: generateUserRecommendations(persona, behavior)
        };
        
        return insights;
        
    } catch (error) {
        console.error('Error getting user behavior insights:', error);
        return { error: error.message };
    }
}

// Helper functions
function getUniqueSessionCount(events) {
    const sessions = new Set(events.map(e => e.sessionId));
    return sessions.size;
}

function getMostCommonActions(events) {
    const actionCounts = {};
    events.forEach(event => {
        actionCounts[event.type] = (actionCounts[event.type] || 0) + 1;
    });
    
    return Object.entries(actionCounts)
        .sort(([,a], [,b]) => b - a)
        .slice(0, 5)
        .map(([action, count]) => ({ action, count }));
}

function calculateEngagementScore(events) {
    if (events.length === 0) return 0;
    
    const recentEvents = events.filter(e => 
        Date.now() - e.timestamp.getTime() < 7 * 24 * 60 * 60 * 1000 // Last 7 days
    );
    
    const engagementFactors = {
        recentActivity: Math.min(recentEvents.length / 10, 1), // 0-1 based on recent events
        sessionFrequency: Math.min(getUniqueSessionCount(recentEvents) / 7, 1), // 0-1 based on daily sessions
        actionDiversity: Math.min(new Set(recentEvents.map(e => e.type)).size / 10, 1) // 0-1 based on action variety
    };
    
    return (engagementFactors.recentActivity + engagementFactors.sessionFrequency + engagementFactors.actionDiversity) / 3;
}

function generateUserRecommendations(persona, behavior) {
    const recommendations = [];
    
    if (persona.conversionLikelihood < 0.5) {
        recommendations.push({
            type: 'engagement',
            message: 'User shows low conversion likelihood - consider re-engagement campaign',
            priority: 'high'
        });
    }
    
    if (behavior.events.length < 10) {
        recommendations.push({
            type: 'onboarding',
            message: 'User has limited platform interaction - provide onboarding assistance',
            priority: 'medium'
        });
    }
    
    const engagementScore = calculateEngagementScore(behavior.events);
    if (engagementScore > 0.8) {
        recommendations.push({
            type: 'upsell',
            message: 'Highly engaged user - good candidate for premium features',
            priority: 'medium'
        });
    }
    
    return recommendations;
}

// Get platform-wide behavior analytics
function getPlatformAnalytics() {
    const stats = userAnalytics.getPersonaStatistics();
    
    return {
        userPersonas: stats,
        trackingEvents: TRACKING_EVENTS,
        systemHealth: {
            trackingActive: true,
            lastUpdated: new Date()
        }
    };
}

// Track demographic information
async function trackDemographicUpdate(userId, demographicData, context = {}) {
    const demographicContext = {
        ...context,
        demographicData: {
            age: demographicData.age,
            gender: demographicData.gender,
            location: demographicData.location,
            businessRole: demographicData.businessRole,
            industryExperience: demographicData.industryExperience,
            companySize: demographicData.companySize,
            annualRevenue: demographicData.annualRevenue,
            businessType: demographicData.businessType
        }
    };

    return await trackUserBehavior(userId, TRACKING_EVENTS.DEMOGRAPHIC_UPDATE, demographicContext);
}

// Track business size detection based on behavior
async function trackBusinessSizeDetection(userId, detectedSize, indicators, context = {}) {
    const businessContext = {
        ...context,
        detectedSize, // 'micro', 'small', 'medium', 'large', 'enterprise'
        indicators: {
            productCount: indicators.productCount || 0,
            orderVolume: indicators.orderVolume || 0,
            revenue: indicators.revenue || 0,
            employeeCount: indicators.employeeCount || 0,
            marketReach: indicators.marketReach || 'local'
        },
        confidence: indicators.confidence || 0.5
    };

    return await trackUserBehavior(userId, TRACKING_EVENTS.BUSINESS_SIZE_DETECTED, businessContext);
}

// Track purchase history analysis
async function trackPurchaseHistoryAnalysis(userId, analysisResults, context = {}) {
    const purchaseContext = {
        ...context,
        analysisResults: {
            totalOrders: analysisResults.totalOrders || 0,
            totalSpent: analysisResults.totalSpent || 0,
            averageOrderValue: analysisResults.averageOrderValue || 0,
            purchaseFrequency: analysisResults.purchaseFrequency || 'unknown',
            preferredCategories: analysisResults.preferredCategories || [],
            seasonalPatterns: analysisResults.seasonalPatterns || {},
            loyaltyScore: analysisResults.loyaltyScore || 0
        }
    };

    return await trackUserBehavior(userId, TRACKING_EVENTS.PURCHASE_HISTORY_ANALYZED, purchaseContext);
}

// Track browsing patterns
async function trackBrowsingPattern(userId, patternType, patternData, context = {}) {
    const browsingContext = {
        ...context,
        patternType, // 'focused_shopper', 'browser', 'researcher', 'impulse_buyer'
        patternData: {
            averageSessionDuration: patternData.averageSessionDuration || 0,
            pagesPerSession: patternData.pagesPerSession || 0,
            bounceRate: patternData.bounceRate || 0,
            returnVisitFrequency: patternData.returnVisitFrequency || 'unknown',
            devicePreference: patternData.devicePreference || 'unknown',
            timeOfDayPreference: patternData.timeOfDayPreference || 'unknown'
        }
    };

    return await trackUserBehavior(userId, TRACKING_EVENTS.BROWSING_PATTERN_DETECTED, browsingContext);
}

// Track device fingerprinting for better user identification
async function trackDeviceFingerprint(userId, deviceData, context = {}) {
    const deviceContext = {
        ...context,
        deviceFingerprint: {
            userAgent: deviceData.userAgent,
            screenResolution: deviceData.screenResolution,
            timezone: deviceData.timezone,
            language: deviceData.language,
            platform: deviceData.platform,
            cookiesEnabled: deviceData.cookiesEnabled,
            javaEnabled: deviceData.javaEnabled,
            plugins: deviceData.plugins || []
        }
    };

    return await trackUserBehavior(userId, TRACKING_EVENTS.DEVICE_FINGERPRINT, deviceContext);
}

// Track location and geographic data
async function trackLocationData(userId, locationData, context = {}) {
    const locationContext = {
        ...context,
        location: {
            country: locationData.country,
            state: locationData.state,
            city: locationData.city,
            zipCode: locationData.zipCode,
            coordinates: locationData.coordinates,
            timezone: locationData.timezone,
            isDomestic: locationData.isDomestic,
            marketSize: locationData.marketSize || 'unknown'
        }
    };

    return await trackUserBehavior(userId, TRACKING_EVENTS.LOCATION_DETECTED, locationContext);
}

// Track referral sources and attribution
async function trackReferralSource(userId, referralData, context = {}) {
    const referralContext = {
        ...context,
        referralData: {
            source: referralData.source, // 'google', 'facebook', 'direct', 'email', etc.
            medium: referralData.medium, // 'organic', 'cpc', 'email', 'social', etc.
            campaign: referralData.campaign,
            term: referralData.term,
            content: referralData.content,
            referrer: referralData.referrer,
            landingPage: referralData.landingPage
        }
    };

    return await trackUserBehavior(userId, TRACKING_EVENTS.REFERRAL_SOURCE_TRACKED, referralContext);
}

// Track engagement milestones
async function trackEngagementMilestone(userId, milestone, milestoneData, context = {}) {
    const milestoneContext = {
        ...context,
        milestone, // 'first_week', 'first_month', 'power_user', 'advocate'
        milestoneData: {
            daysActive: milestoneData.daysActive || 0,
            totalSessions: milestoneData.totalSessions || 0,
            totalTimeSpent: milestoneData.totalTimeSpent || 0,
            featuresUsed: milestoneData.featuresUsed || [],
            achievementDate: milestoneData.achievementDate || new Date()
        }
    };

    return await trackUserBehavior(userId, TRACKING_EVENTS.ENGAGEMENT_MILESTONE, milestoneContext);
}

// Track conversion funnel progression
async function trackConversionFunnelStep(userId, funnelStep, stepData, context = {}) {
    const funnelContext = {
        ...context,
        funnelStep, // 'awareness', 'interest', 'consideration', 'intent', 'evaluation', 'purchase'
        stepData: {
            stepNumber: stepData.stepNumber || 0,
            timeToReachStep: stepData.timeToReachStep || 0,
            previousStep: stepData.previousStep,
            conversionProbability: stepData.conversionProbability || 0,
            dropOffRisk: stepData.dropOffRisk || 0
        }
    };

    return await trackUserBehavior(userId, TRACKING_EVENTS.CONVERSION_FUNNEL_STEP, funnelContext);
}

// Enhanced user insights with demographic and business data
async function getEnhancedUserInsights(userId) {
    try {
        const basicInsights = await getUserBehaviorInsights(userId);
        const behavior = userAnalytics.getUserBehavior(userId);

        if (!behavior) {
            return { error: 'No behavior data found' };
        }

        // Extract demographic insights
        const demographicEvents = behavior.events.filter(e => e.type === TRACKING_EVENTS.DEMOGRAPHIC_UPDATE);
        const latestDemographics = demographicEvents.length > 0 ?
            demographicEvents[demographicEvents.length - 1].data.demographicData : null;

        // Extract business size insights
        const businessSizeEvents = behavior.events.filter(e => e.type === TRACKING_EVENTS.BUSINESS_SIZE_DETECTED);
        const latestBusinessSize = businessSizeEvents.length > 0 ?
            businessSizeEvents[businessSizeEvents.length - 1].data : null;

        // Extract purchase history insights
        const purchaseEvents = behavior.events.filter(e => e.type === TRACKING_EVENTS.PURCHASE_HISTORY_ANALYZED);
        const latestPurchaseAnalysis = purchaseEvents.length > 0 ?
            purchaseEvents[purchaseEvents.length - 1].data.analysisResults : null;

        // Extract browsing patterns
        const browsingEvents = behavior.events.filter(e => e.type === TRACKING_EVENTS.BROWSING_PATTERN_DETECTED);
        const latestBrowsingPattern = browsingEvents.length > 0 ?
            browsingEvents[browsingEvents.length - 1].data : null;

        const enhancedInsights = {
            ...basicInsights,
            demographics: latestDemographics,
            businessProfile: latestBusinessSize,
            purchaseProfile: latestPurchaseAnalysis,
            browsingProfile: latestBrowsingPattern,
            engagementMilestones: getEngagementMilestones(behavior.events),
            conversionFunnelPosition: getCurrentFunnelPosition(behavior.events),
            riskFactors: calculateRiskFactors(behavior.events)
        };

        return enhancedInsights;

    } catch (error) {
        console.error('Error getting enhanced user insights:', error);
        return { error: error.message };
    }
}

// Helper functions for enhanced insights
function getEngagementMilestones(events) {
    const milestoneEvents = events.filter(e => e.type === TRACKING_EVENTS.ENGAGEMENT_MILESTONE);
    return milestoneEvents.map(event => ({
        milestone: event.data.milestone,
        achievedAt: event.timestamp,
        data: event.data.milestoneData
    }));
}

function getCurrentFunnelPosition(events) {
    const funnelEvents = events.filter(e => e.type === TRACKING_EVENTS.CONVERSION_FUNNEL_STEP);
    if (funnelEvents.length === 0) return null;

    const latestFunnelEvent = funnelEvents[funnelEvents.length - 1];
    return {
        currentStep: latestFunnelEvent.data.funnelStep,
        stepNumber: latestFunnelEvent.data.stepData.stepNumber,
        conversionProbability: latestFunnelEvent.data.stepData.conversionProbability,
        timeInStep: Date.now() - latestFunnelEvent.timestamp.getTime()
    };
}

function calculateRiskFactors(events) {
    const riskFactors = [];

    // Check for abandonment patterns
    const abandonmentEvents = events.filter(e => e.type === TRACKING_EVENTS.ABANDONMENT_EVENT);
    if (abandonmentEvents.length > 2) {
        riskFactors.push({
            type: 'high_abandonment',
            severity: 'high',
            description: 'User shows pattern of abandoning processes'
        });
    }

    // Check for low engagement
    const recentEvents = events.filter(e =>
        Date.now() - e.timestamp.getTime() < 7 * 24 * 60 * 60 * 1000 // Last 7 days
    );

    if (recentEvents.length < 5) {
        riskFactors.push({
            type: 'low_engagement',
            severity: 'medium',
            description: 'User engagement has decreased recently'
        });
    }

    return riskFactors;
}

module.exports = {
    trackUserBehavior,
    trackPageView,
    trackSearch,
    trackProductInteraction,
    trackBusinessAction,
    trackAIAssistantUsage,
    getUserBehaviorInsights,
    getPlatformAnalytics,

    // Enhanced tracking functions
    trackDemographicUpdate,
    trackBusinessSizeDetection,
    trackPurchaseHistoryAnalysis,
    trackBrowsingPattern,
    trackDeviceFingerprint,
    trackLocationData,
    trackReferralSource,
    trackEngagementMilestone,
    trackConversionFunnelStep,
    getEnhancedUserInsights,

    TRACKING_EVENTS
};
