<template>
    <div class="ai-assistant" :class="{ 'ai-assistant--open': isOpen }">
        <!-- Toggle Button -->
        <button 
            class="ai-assistant__toggle"
            @click="toggleAssistant"
            :class="{ 'ai-assistant__toggle--open': isOpen }"
            aria-label="AI Assistant">
            <svg v-if="!isOpen" class="ai-assistant__icon" viewBox="0 0 24 24">
                <path d="M12 2C13.1 2 14 2.9 14 4C14 5.1 13.1 6 12 6C10.9 6 10 5.1 10 4C10 2.9 10.9 2 12 2ZM21 9V7L15 1H5C3.89 1 3 1.89 3 3V21C3 22.11 3.89 23 5 23H11V21H5V3H13V9H21Z"/>
            </svg>
            <svg v-else class="ai-assistant__icon" viewBox="0 0 24 24">
                <path d="M19 6.41L17.59 5 12 10.59 6.41 5 5 6.41 10.59 12 5 17.59 6.41 19 12 13.41 17.59 19 19 17.59 13.41 12Z"/>
            </svg>
        </button>

        <!-- Chat Interface -->
        <div v-if="isOpen" class="ai-assistant__chat">
            <div class="ai-assistant__header">
                <div class="ai-assistant__title">
                    <svg class="ai-assistant__header-icon" viewBox="0 0 24 24">
                        <path d="M12 2C13.1 2 14 2.9 14 4C14 5.1 13.1 6 12 6C10.9 6 10 5.1 10 4C10 2.9 10.9 2 12 2Z"/>
                    </svg>
                    AI Assistant
                </div>
                <button @click="closeAssistant" class="ai-assistant__close" aria-label="Close">
                    <svg viewBox="0 0 24 24">
                        <path d="M19 6.41L17.59 5 12 10.59 6.41 5 5 6.41 10.59 12 5 17.59 6.41 19 12 13.41 17.59 19 19 17.59 13.41 12Z"/>
                    </svg>
                </button>
            </div>

            <div class="ai-assistant__messages" ref="messagesContainer">
                <!-- Welcome Message -->
                <div v-if="messages.length === 0" class="ai-assistant__message ai-assistant__message--ai">
                    <div class="ai-assistant__message-content">
                        <p>👋 Hi! I'm your AI assistant. I can help you navigate the platform, change settings, or answer questions about CanIDeal.</p>
                        <div v-if="suggestions.length > 0" class="ai-assistant__suggestions">
                            <p class="ai-assistant__suggestions-title">Try asking:</p>
                            <button 
                                v-for="suggestion in suggestions" 
                                :key="suggestion"
                                @click="sendMessage(suggestion)"
                                class="ai-assistant__suggestion">
                                {{ suggestion }}
                            </button>
                        </div>
                    </div>
                </div>

                <!-- Chat Messages -->
                <div 
                    v-for="(message, index) in messages" 
                    :key="index"
                    class="ai-assistant__message"
                    :class="{
                        'ai-assistant__message--user': message.type === 'user',
                        'ai-assistant__message--ai': message.type === 'ai',
                        'ai-assistant__message--error': message.type === 'error'
                    }">
                    <div class="ai-assistant__message-content">
                        {{ message.content }}
                    </div>
                    <div class="ai-assistant__message-time">
                        {{ formatTime(message.timestamp) }}
                    </div>
                </div>

                <!-- Loading indicator -->
                <div v-if="isLoading" class="ai-assistant__message ai-assistant__message--ai">
                    <div class="ai-assistant__message-content">
                        <div class="ai-assistant__loading">
                            <span></span>
                            <span></span>
                            <span></span>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Input Area -->
            <div class="ai-assistant__input">
                <div class="ai-assistant__input-container">
                    <textarea 
                        v-model="currentMessage"
                        @keydown.enter.prevent="handleEnterKey"
                        @input="adjustTextareaHeight"
                        ref="messageInput"
                        class="ai-assistant__textarea"
                        placeholder="Ask me anything about CanIDeal..."
                        rows="1"
                        :disabled="isLoading"></textarea>
                    <button 
                        @click="sendCurrentMessage"
                        :disabled="!currentMessage.trim() || isLoading"
                        class="ai-assistant__send">
                        <svg viewBox="0 0 24 24">
                            <path d="M2,21L23,12L2,3V10L17,12L2,14V21Z"/>
                        </svg>
                    </button>
                </div>
            </div>
        </div>
    </div>
</template>

<script>
export default {
    name: 'AIAssistant',
    data() {
        return {
            isOpen: false,
            messages: [],
            currentMessage: '',
            isLoading: false,
            suggestions: [],
            context: {}
        };
    },
    mounted() {
        this.loadSuggestions();
        this.updateContext();
    },
    methods: {
        toggleAssistant() {
            this.isOpen = !this.isOpen;
            if (this.isOpen) {
                this.$nextTick(() => {
                    this.$refs.messageInput?.focus();
                });
            }
        },
        
        closeAssistant() {
            this.isOpen = false;
        },

        async sendMessage(message) {
            // Input validation
            if (!message || typeof message !== 'string') {
                return;
            }

            const trimmedMessage = message.trim();
            if (trimmedMessage.length === 0) {
                return;
            }

            if (trimmedMessage.length > 1000) {
                this.showError('Message is too long. Please keep it under 1000 characters.');
                return;
            }

            // Prevent sending while loading
            if (this.isLoading) {
                return;
            }

            // Add user message
            this.messages.push({
                type: 'user',
                content: trimmedMessage,
                timestamp: new Date()
            });

            this.currentMessage = '';
            this.isLoading = true;
            this.scrollToBottom();

            try {
                const response = await this.$http.request({
                    url: '/ai-assistant/chat',
                    method: 'POST',
                    data: {
                        message: trimmedMessage,
                        context: this.sanitizeContext(this.context)
                    },
                    timeout: 30000 // 30 second timeout
                });

                // Validate response
                if (!response || !response.data || !response.data.message) {
                    throw new Error('Invalid response from AI service');
                }

                // Add AI response
                this.messages.push({
                    type: 'ai',
                    content: response.data.message,
                    timestamp: new Date()
                });

            } catch (error) {
                console.error('AI Assistant error:', error);

                let errorMessage = 'Sorry, I\'m having trouble right now. Please try again later.';

                // Provide more specific error messages
                if (error.response) {
                    if (error.response.status === 429) {
                        errorMessage = 'I\'m receiving too many requests right now. Please wait a moment and try again.';
                    } else if (error.response.status === 422) {
                        errorMessage = 'There was an issue with your message. Please try rephrasing it.';
                    } else if (error.response.status === 503) {
                        errorMessage = 'The AI service is temporarily unavailable. Please try again later.';
                    }
                } else if (error.code === 'ECONNABORTED') {
                    errorMessage = 'The request timed out. Please try again with a shorter message.';
                }

                this.messages.push({
                    type: 'error',
                    content: errorMessage,
                    timestamp: new Date()
                });
            } finally {
                this.isLoading = false;
                this.scrollToBottom();
            }
        },

        showError(message) {
            this.messages.push({
                type: 'error',
                content: message,
                timestamp: new Date()
            });
            this.scrollToBottom();
        },

        sanitizeContext(context) {
            if (!context || typeof context !== 'object') {
                return {};
            }

            // Only include safe context properties
            const safeContext = {};
            const allowedFields = ['currentPage', 'userRole', 'sessionId'];

            for (const field of allowedFields) {
                if (context[field] && typeof context[field] === 'string') {
                    safeContext[field] = context[field].substring(0, 200);
                }
            }

            return safeContext;
        },

        sendCurrentMessage() {
            this.sendMessage(this.currentMessage);
        },

        handleEnterKey(event) {
            if (!event.shiftKey) {
                this.sendCurrentMessage();
            }
        },

        adjustTextareaHeight() {
            const textarea = this.$refs.messageInput;
            if (textarea) {
                textarea.style.height = 'auto';
                textarea.style.height = Math.min(textarea.scrollHeight, 120) + 'px';
            }
        },

        scrollToBottom() {
            this.$nextTick(() => {
                const container = this.$refs.messagesContainer;
                if (container) {
                    container.scrollTop = container.scrollHeight;
                }
            });
        },

        async loadSuggestions() {
            try {
                const response = await this.$http.request({
                    url: '/ai-assistant/suggestions',
                    method: 'GET',
                    timeout: 10000 // 10 second timeout
                });

                // Validate response
                if (response && response.data && Array.isArray(response.data.suggestions)) {
                    // Filter and validate suggestions
                    this.suggestions = response.data.suggestions
                        .filter(s => typeof s === 'string' && s.trim().length > 0)
                        .map(s => s.trim().substring(0, 200))
                        .slice(0, 5);
                } else if (response && Array.isArray(response.suggestions)) {
                    // Handle direct suggestions array
                    this.suggestions = response.suggestions
                        .filter(s => typeof s === 'string' && s.trim().length > 0)
                        .map(s => s.trim().substring(0, 200))
                        .slice(0, 5);
                } else {
                    this.suggestions = this.getDefaultSuggestions();
                }
            } catch (error) {
                console.error('Error loading suggestions:', error);
                this.suggestions = this.getDefaultSuggestions();
            }
        },

        getDefaultSuggestions() {
            return [
                "How do I change my password?",
                "How do I update my profile?",
                "How do I contact support?",
                "What features are available to me?",
                "How do I navigate the platform?"
            ];
        },

        updateContext() {
            this.context = {
                currentPage: this.$route?.path || window.location.pathname,
                userAgent: navigator.userAgent,
                timestamp: new Date().toISOString(),
                sessionId: this.getSessionId()
            };

            // Update server-side context
            this.updateServerContext();
        },

        async updateServerContext() {
            try {
                await this.$http.request({
                    url: '/ai-assistant/update-context',
                    method: 'POST',
                    data: {
                        contextUpdates: {
                            currentPage: this.context.currentPage,
                            lastActivity: new Date().toISOString(),
                            sessionId: this.context.sessionId
                        }
                    }
                });
            } catch (error) {
                console.error('Failed to update server context:', error);
            }
        },

        getSessionId() {
            let sessionId = sessionStorage.getItem('ai_session_id');
            if (!sessionId) {
                sessionId = `session_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
                sessionStorage.setItem('ai_session_id', sessionId);
            }
            return sessionId;
        },

        formatTime(timestamp) {
            return new Date(timestamp).toLocaleTimeString([], { 
                hour: '2-digit', 
                minute: '2-digit' 
            });
        }
    }
};
</script>

<style lang="scss" scoped>
.ai-assistant {
    position: fixed;
    bottom: 20px;
    right: 20px;
    z-index: 1000;
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;

    &__toggle {
        width: 60px;
        height: 60px;
        border-radius: 50%;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        border: none;
        cursor: pointer;
        box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
        transition: all 0.3s ease;
        display: flex;
        align-items: center;
        justify-content: center;

        &:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 25px rgba(0, 0, 0, 0.2);
        }

        &--open {
            background: #ff6b6b;
        }
    }

    &__icon {
        width: 24px;
        height: 24px;
        fill: white;
    }

    &__chat {
        position: absolute;
        bottom: 80px;
        right: 0;
        width: 380px;
        height: 500px;
        background: white;
        border-radius: 12px;
        box-shadow: 0 10px 40px rgba(0, 0, 0, 0.15);
        display: flex;
        flex-direction: column;
        overflow: hidden;
        animation: slideUp 0.3s ease;
    }

    &__header {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        padding: 16px 20px;
        display: flex;
        align-items: center;
        justify-content: space-between;
    }

    &__title {
        display: flex;
        align-items: center;
        font-weight: 600;
        font-size: 16px;
    }

    &__header-icon {
        width: 20px;
        height: 20px;
        fill: white;
        margin-right: 8px;
    }

    &__close {
        background: none;
        border: none;
        color: white;
        cursor: pointer;
        padding: 4px;
        border-radius: 4px;
        transition: background-color 0.2s;

        &:hover {
            background-color: rgba(255, 255, 255, 0.1);
        }

        svg {
            width: 20px;
            height: 20px;
            fill: currentColor;
        }
    }

    &__messages {
        flex: 1;
        overflow-y: auto;
        padding: 16px;
        display: flex;
        flex-direction: column;
        gap: 12px;
    }

    &__message {
        display: flex;
        flex-direction: column;
        max-width: 85%;

        &--user {
            align-self: flex-end;

            .ai-assistant__message-content {
                background: #667eea;
                color: white;
                border-radius: 18px 18px 4px 18px;
            }
        }

        &--ai {
            align-self: flex-start;

            .ai-assistant__message-content {
                background: #f1f3f5;
                color: #333;
                border-radius: 18px 18px 18px 4px;
            }
        }

        &--error {
            align-self: flex-start;

            .ai-assistant__message-content {
                background: #ffe0e0;
                color: #d63031;
                border-radius: 18px 18px 18px 4px;
            }
        }
    }

    &__message-content {
        padding: 12px 16px;
        font-size: 14px;
        line-height: 1.4;
        word-wrap: break-word;
    }

    &__message-time {
        font-size: 11px;
        color: #999;
        margin-top: 4px;
        align-self: flex-end;
    }

    &__suggestions {
        margin-top: 12px;

        &-title {
            font-size: 12px;
            color: #666;
            margin-bottom: 8px;
        }
    }

    &__suggestion {
        display: block;
        width: 100%;
        background: white;
        border: 1px solid #e0e0e0;
        border-radius: 8px;
        padding: 8px 12px;
        margin-bottom: 6px;
        font-size: 13px;
        text-align: left;
        cursor: pointer;
        transition: all 0.2s;

        &:hover {
            background: #f8f9fa;
            border-color: #667eea;
        }
    }

    &__loading {
        display: flex;
        gap: 4px;

        span {
            width: 8px;
            height: 8px;
            border-radius: 50%;
            background: #999;
            animation: bounce 1.4s ease-in-out infinite both;

            &:nth-child(1) { animation-delay: -0.32s; }
            &:nth-child(2) { animation-delay: -0.16s; }
        }
    }

    &__input {
        border-top: 1px solid #e0e0e0;
        padding: 16px;
    }

    &__input-container {
        display: flex;
        align-items: flex-end;
        gap: 8px;
    }

    &__textarea {
        flex: 1;
        border: 1px solid #e0e0e0;
        border-radius: 20px;
        padding: 10px 16px;
        font-size: 14px;
        resize: none;
        outline: none;
        transition: border-color 0.2s;
        max-height: 120px;

        &:focus {
            border-color: #667eea;
        }

        &:disabled {
            background: #f8f9fa;
            cursor: not-allowed;
        }
    }

    &__send {
        width: 40px;
        height: 40px;
        border-radius: 50%;
        background: #667eea;
        border: none;
        cursor: pointer;
        display: flex;
        align-items: center;
        justify-content: center;
        transition: all 0.2s;

        &:hover:not(:disabled) {
            background: #5a67d8;
            transform: scale(1.05);
        }

        &:disabled {
            background: #ccc;
            cursor: not-allowed;
            transform: none;
        }

        svg {
            width: 20px;
            height: 20px;
            fill: white;
        }
    }
}

@keyframes slideUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes bounce {
    0%, 80%, 100% {
        transform: scale(0);
    }
    40% {
        transform: scale(1);
    }
}

@media (max-width: 480px) {
    .ai-assistant {
        bottom: 10px;
        right: 10px;

        &__chat {
            width: calc(100vw - 20px);
            height: 70vh;
            bottom: 70px;
            right: -10px;
        }
    }
}
</style>
