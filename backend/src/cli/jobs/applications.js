
'use strict';
const queue = require('../../services/queue');
const mongoose = require('mongoose');
const constants = require('../../models/constants');
const sentry = require('../../services/sentry');
const emailService = require('../../services/email');

const logger = sentry.logger('job.applications');

module.exports = function (agenda) {
    // a job to check if applicaion is expired
    agenda.define(queue.constants.APPLICATION.CHECK, (job, done) => {
        checkApplicationsExpiredDate()
            .then(() => {
                done();
            })
            .catch(err => {
                console.error(err);
                logger(err, {
                    tags: {
                        queue: queue.constants.APPLICATION.CHECK
                    }
                });
                done(err);
            });
    });
    agenda.every(
        '5 minutes',
        queue.constants.APPLICATION.CHECK,
        {},
        {
            skipImmediate: true
        }
    );

    // a job to remind the draft application of submitting
    agenda.define(queue.constants.APPLICATION.DRAFT_REMINDER, (job, done) => {
        checkDraftApplication()
            .then(() => {
                done();
            })
            .catch(err => {
                console.error(err);
                logger(err, {
                    tags: {
                        queue: queue.constants.APPLICATION.DRAFT_REMINDER
                    }
                });
                done(err);
            });
    });
    agenda.every(
        '1 day',
        queue.constants.APPLICATION.DRAFT_REMINDER,
        {},
        {
            skipImmediate: true
        }
    );
};

async function checkApplicationsExpiredDate() {
    const { Application, SystemLog } = mongoose.models;

    const now = new Date();
    const expiredFiles = await Application.find({ 'files.expirationDate': { $lt: now } }, { _id: 1 });
    const expiredFileIds = expiredFiles.map(file => file._id);

    const applicationList = await Application.find({
        status: constants.APPLICATION.STATUS.ACCEPTED,
        $or: [
            { expiredAt: { $lt: now } },
            { _id: { $in: expiredFileIds } }
        ]
    });

    for (const application of applicationList) {
        application.set({
            status: constants.APPLICATION.STATUS.EXPIRED
        });
        await application.save();
        await SystemLog.scriptApplicationExpired(application);
    }
}

async function checkDraftApplication() {
    const { Application, User, SystemLog } = mongoose.models;
    let userList = [];

    const unSubmmittedRetailerApplications = await Application.aggregate([
        {
            $match: {
                $expr: {
                    $gt: [
                        { $subtract: [new Date(), "$submittedAt"] },
                        { $multiply: [2, 24, 60, 60, 1000] } // 2 days in milliseconds
                    ]
                },
                status: constants.APPLICATION.STATUS.DRAFT,
                type: constants.USER.ROLE.RETAILER
            }
        }
    ]);

    if (unSubmmittedRetailerApplications.length > 0) {
        for (let i = 0; i < unSubmmittedRetailerApplications.length; i++) {
            const application = unSubmmittedRetailerApplications[i];
            const retailers = await User.find({ _retailer: application._retailer, lastLoginBy: constants.USER.ROLE.RETAILER });
            userList.push(...retailers);
        }
    }

    const unSubmmittedVendorApplications = await Application.aggregate([
        {
            $match: {
                $expr: {
                    $gt: [
                        { $subtract: [new Date(), "$submittedAt"] },
                        { $multiply: [2, 24, 60, 60, 1000] } // 2 days in milliseconds
                    ]
                },
                status: constants.APPLICATION.STATUS.DRAFT,
                type: constants.USER.ROLE.VENDOR
            }
        }
    ]);

    if (unSubmmittedVendorApplications.length > 0) {
        for (let i = 0; i < unSubmmittedVendorApplications.length; i++) {
            const application = unSubmmittedVendorApplications[i];
            const vendors = await User.find({ _vendor: application._vendor, lastLoginBy: constants.USER.ROLE.VENDOR });
            userList.push(...vendors);
        }
    }
    
    if (userList.length > 0) {
        userList.forEach(async (user) => {
            await emailService.create.reminderEmailForDraft({ user });
        });
    }
}
